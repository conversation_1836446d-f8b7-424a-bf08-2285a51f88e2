# Konfiguracja środowiska - skopiuj do .env i dostosuj wartości

# Środowisko aplikacji
APP_ENV=production
APP_DEBUG=false
APP_URL=http://localhost
APP_NAME="System Magazynowy"

# Baza danych
DB_HOST=localhost
DB_PORT=3306
DB_NAME=magazyn_system
DB_USERNAME=root
DB_PASSWORD=

# Sesje
SESSION_LIFETIME=7200
SESSION_NAME=MAGAZYN_SESSION

# Bezpieczeństwo
CSRF_TOKEN_NAME=csrf_token
PASSWORD_MIN_LENGTH=8

# Upload plików
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp
UPLOAD_PATH=public/uploads

# Email (opcjonalnie)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="System Magazynowy"

# Raporty
REPORTS_PER_PAGE=50
ITEMS_PER_PAGE=20

# Cache
CACHE_ENABLED=true
CACHE_LIFETIME=3600

# Logi
LOG_ENABLED=true
LOG_LEVEL=error
LOG_PATH=logs/

# Timezone
TIMEZONE=Europe/Warsaw
