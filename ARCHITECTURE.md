# Architektura Systemu Magazynowego

## Przegląd Technologii

### Backend
- **PHP 8.1+** - Nowoczesny PHP z wykorzystaniem OOP i najlepszych praktyk
- **MySQL 8.0+** - Baza danych z pełnym wsparciem dla JSON i zaawansowanych indeksów
- **Composer** - Zarządzanie zależnościami
- **PSR-4** - Autoloading klas
- **PDO** - Bezpieczne połączenia z bazą danych

### Frontend
- **Bootstrap 5** - Responsywny framework CSS
- **Alpine.js** - Lekki framework JavaScript dla interaktywności
- **Vanilla JavaScript** - Dla zaawansowanych funkcji
- **CSS Grid/Flexbox** - Nowoczesne układy

### Bezpieczeństwo
- **Password hashing** - bcrypt/Argon2
- **CSRF protection** - Tokeny <PERSON>abe<PERSON>ające
- **SQL injection prevention** - Prepared statements
- **XSS protection** - Escape output
- **Session management** - Bezpieczne sesje

## Struktura Bazy Danych

### Główne Tabele

1. **users** - Użytkownicy systemu (administratorzy, geodeci)
2. **categories** - Kategorie przedmiotów
3. **items** - Przedmioty w magazynie
4. **item_images** - Zdjęcia przedmiotów
5. **reservations** - Rezerwacje przedmiotów
6. **loans** - Wypożyczenia
7. **transfer_requests** - Prośby o przekazanie
8. **transfers** - Historia wszystkich transferów
9. **user_sessions** - Sesje użytkowników

### Kluczowe Relacje

- **items** ↔ **categories** (Many-to-One)
- **items** ↔ **item_images** (One-to-Many)
- **items** ↔ **loans** (One-to-Many)
- **users** ↔ **loans** (One-to-Many jako borrower/lender)
- **reservations** ↔ **loans** (One-to-One opcjonalnie)

## Architektura Aplikacji

### Struktura Katalogów
```
/
├── config/          # Konfiguracja aplikacji
├── src/             # Kod źródłowy
│   ├── Controllers/ # Kontrolery MVC
│   ├── Models/      # Modele danych
│   ├── Services/    # Logika biznesowa
│   ├── Utils/       # Narzędzia pomocnicze
│   └── Database/    # Klasy bazy danych
├── public/          # Pliki publiczne
│   ├── assets/      # CSS, JS, obrazy
│   ├── uploads/     # Zdjęcia przedmiotów
│   └── index.php    # Punkt wejścia
├── templates/       # Szablony HTML
├── vendor/          # Zależności Composer
└── tests/           # Testy jednostkowe
```

### Wzorzec MVC

**Model** - Reprezentacja danych i logika biznesowa
- `User`, `Item`, `Category`, `Loan`, `Reservation`
- Walidacja danych
- Operacje CRUD

**View** - Prezentacja danych
- Szablony HTML z PHP
- Komponenty Bootstrap
- Alpine.js dla interaktywności

**Controller** - Logika aplikacji
- `AuthController` - Uwierzytelnianie
- `ItemController` - Zarządzanie przedmiotami
- `LoanController` - Wypożyczenia
- `ReportController` - Raporty

## Funkcjonalności Systemu

### 1. Uwierzytelnianie i Autoryzacja
- Logowanie użytkowników
- Role: Administrator/Magazynier, Geodeta
- Sesje z automatycznym wygasaniem
- Zabezpieczenia CSRF

### 2. Zarządzanie Przedmiotami
- CRUD przedmiotów
- Upload wielu zdjęć
- Kategoryzacja
- Wyszukiwanie i filtrowanie
- Status przedmiotów

### 3. System Wypożyczeń
- Rezerwacje przedmiotów
- Wydawanie sprzętu
- Zwroty
- Przekazywanie między użytkownikami
- Historia wypożyczeń

### 4. Raporty
- Stan magazynu
- Wypożyczenia użytkowników
- Historia przedmiotów
- Eksport do CSV/PDF

## Bezpieczeństwo

### Uwierzytelnianie
- Hashowanie haseł (bcrypt)
- Sesje z tokenami
- Automatyczne wylogowanie

### Autoryzacja
- Kontrola dostępu na poziomie kontrolerów
- Sprawdzanie uprawnień do zasobów
- Separacja funkcji według ról

### Ochrona Danych
- Prepared statements (PDO)
- Walidacja i sanityzacja danych
- Escape output (XSS protection)
- CSRF tokens

### Upload Plików
- Walidacja typów plików
- Ograniczenia rozmiaru
- Bezpieczne nazwy plików
- Skanowanie antywirusowe (opcjonalnie)

## Wydajność

### Baza Danych
- Indeksy na kluczowych kolumnach
- Optymalizacja zapytań
- Paginacja wyników
- Cache zapytań

### Frontend
- Minifikacja CSS/JS
- Lazy loading obrazów
- Kompresja zdjęć
- CDN dla statycznych zasobów

### Backend
- Autoloading klas
- Optymalizacja PHP (OPcache)
- Gzip compression
- Cache sesji

## Skalowalność

### Pozioma
- Separacja bazy danych
- Load balancing
- CDN dla plików statycznych
- Cache Redis/Memcached

### Pionowa
- Optymalizacja zapytań
- Indeksy bazy danych
- Partycjonowanie tabel
- Archiwizacja starych danych

## Rozszerzalność

### API
- RESTful endpoints
- JSON responses
- Dokumentacja OpenAPI
- Versioning

### Integracje
- Webhook notifications
- Email notifications
- SMS alerts
- External systems integration

### Moduły
- Plugin architecture
- Event system
- Custom fields
- Workflow automation
