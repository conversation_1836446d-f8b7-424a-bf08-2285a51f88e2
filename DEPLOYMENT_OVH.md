# Wdrożenie na Hosting OVH - Instrukcja Krok po Krok

## 🚀 Przygotowanie plików

### 1. Pliki do przegrania przez FTP

Prześlij **wszystkie** pliki z projektu na serwer OVH przez FTP:

```
/
├── config/              # Cała zawartość
├── src/                 # Cała zawartość  
├── templates/           # Cała zawartość
├── public/              # Cała zawartość
├── vendor/              # Jeśli masz Composer lokalnie
├── .env                 # Plik konfiguracyjny
├── .htaccess           # Konfiguracja Apache
└── database_schema.sql  # Struktura bazy danych
```

### 2. Struktura na serwerze OVH

**WAŻNE:** Zawartość katalogu `public/` musi być w katalogu głównym domeny!

```
Twoja domena (www)/
├── index.php           # Z katalogu public/
├── assets/             # Z katalogu public/assets/
├── uploads/            # Z katalogu public/uploads/
├── .htaccess          # Z katalogu public/
├── config/            # Cały katalog
├── src/               # Cały katalog
├── templates/         # Cały katalog
├── vendor/            # Cały katalog (jeśli masz)
└── .env              # Plik konfiguracyjny
```

## 🗄️ Konfiguracja bazy danych

### 1. Utworzenie bazy danych w panelu OVH

1. Zaloguj się do panelu OVH
2. Przejdź do sekcji "Bazy danych"
3. Utwórz nową bazę danych MySQL
4. Zapisz dane dostępowe:
   - Host (np. `mysql51-66.perso`)
   - Nazwa bazy danych
   - Użytkownik
   - Hasło

### 2. Import struktury bazy danych

1. Przejdź do phpMyAdmin w panelu OVH
2. Wybierz swoją bazę danych
3. Kliknij zakładkę "Import"
4. Wybierz plik `database_schema.sql`
5. Kliknij "Wykonaj"

### 3. Sprawdzenie importu

Po imporcie powinieneś zobaczyć następujące tabele:
- `users`
- `categories`
- `items`
- `item_images`
- `reservations`
- `loans`
- `transfer_requests`
- `transfers`

## ⚙️ Konfiguracja aplikacji

### 1. Edycja pliku .env

Otwórz plik `.env` i dostosuj ustawienia:

```env
# Środowisko aplikacji
APP_ENV=production
APP_DEBUG=false
APP_URL=https://twoja-domena.com
APP_NAME="System Magazynowy"

# Baza danych - WPISZ SWOJE DANE!
DB_HOST=mysql51-66.perso
DB_PORT=3306
DB_NAME=twoja_baza_danych
DB_USERNAME=twoj_uzytkownik
DB_PASSWORD=twoje_haslo

# Sesje
SESSION_LIFETIME=7200
SESSION_NAME=MAGAZYN_SESSION

# Bezpieczeństwo
CSRF_TOKEN_NAME=csrf_token
PASSWORD_MIN_LENGTH=8

# Upload plików
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp
UPLOAD_PATH=uploads

# Timezone
TIMEZONE=Europe/Warsaw
```

### 2. Sprawdzenie uprawnień katalogów

Upewnij się, że katalog `uploads/` ma uprawnienia do zapisu (755 lub 775).

## 🔧 Testowanie instalacji

### 1. Sprawdzenie strony głównej

1. Otwórz przeglądarkę
2. Przejdź do swojej domeny
3. Powinieneś zobaczyć stronę logowania

### 2. Pierwsze logowanie

**Domyślne dane logowania:**
- **Użytkownik:** `admin`
- **Hasło:** `admin123`

**⚠️ WAŻNE: Zmień hasło natychmiast po pierwszym logowaniu!**

### 3. Test funkcjonalności

Po zalogowaniu sprawdź:
- ✅ Dashboard się ładuje
- ✅ Lista przedmiotów działa
- ✅ Można dodać kategorię
- ✅ Można dodać przedmiot
- ✅ Upload zdjęć działa

## 🛠️ Rozwiązywanie problemów

### Problem: Błąd 500 - Internal Server Error

**Przyczyny i rozwiązania:**

1. **Błędne uprawnienia plików**
   ```bash
   # Ustaw uprawnienia (jeśli masz dostęp SSH)
   chmod 755 katalogi/
   chmod 644 pliki/
   chmod 775 uploads/
   ```

2. **Błędna konfiguracja bazy danych**
   - Sprawdź dane w pliku `.env`
   - Upewnij się, że baza danych istnieje
   - Sprawdź czy tabele zostały utworzone

3. **Brakujące rozszerzenia PHP**
   - Sprawdź w panelu OVH czy są włączone:
     - PDO MySQL
     - GD lub Imagick
     - mbstring
     - fileinfo

### Problem: Nie można się zalogować

1. **Sprawdź czy tabela users istnieje**
2. **Sprawdź czy jest użytkownik admin:**
   ```sql
   SELECT * FROM users WHERE username = 'admin';
   ```
3. **Jeśli brak, dodaj ręcznie:**
   ```sql
   INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_active) 
   VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Systemu', 'admin', 1);
   ```

### Problem: Błędy uploadu zdjęć

1. **Sprawdź uprawnienia katalogu uploads/**
2. **Sprawdź ustawienia PHP:**
   - `upload_max_filesize = 10M`
   - `post_max_size = 20M`
   - `max_execution_time = 300`

### Problem: Błędy sesji

1. **Sprawdź czy katalog sesji ma uprawnienia do zapisu**
2. **W pliku .env ustaw:**
   ```env
   SESSION_LIFETIME=7200
   ```

## 📧 Konfiguracja email (opcjonalnie)

Jeśli chcesz włączyć powiadomienia email:

```env
MAIL_HOST=ssl0.ovh.net
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=twoje-haslo-email
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="System Magazynowy"
```

## 🔒 Zabezpieczenia produkcyjne

### 1. Zmiana hasła administratora

1. Zaloguj się jako admin
2. Przejdź do "Profil"
3. Zmień hasło na silne

### 2. Ukrycie pliku .env

Dodaj do `.htaccess` w katalogu głównym:
```apache
<Files ".env">
    Order allow,deny
    Deny from all
</Files>
```

### 3. Wyłączenie trybu debug

W pliku `.env`:
```env
APP_DEBUG=false
```

## 📞 Wsparcie

### Jeśli coś nie działa:

1. **Sprawdź logi błędów** w panelu OVH
2. **Włącz tymczasowo debug:**
   ```env
   APP_DEBUG=true
   ```
3. **Sprawdź czy wszystkie pliki zostały przegrane**
4. **Sprawdź uprawnienia katalogów**

### Najczęstsze błędy:

- **Błąd połączenia z bazą** → Sprawdź dane w .env
- **Błąd 404** → Sprawdź czy .htaccess został przegrany
- **Błąd 500** → Sprawdź logi błędów i uprawnienia
- **Nie działa upload** → Sprawdź uprawnienia katalogu uploads/

---

## ✅ Checklist wdrożenia

- [ ] Wszystkie pliki przegrane przez FTP
- [ ] Baza danych utworzona i zaimportowana
- [ ] Plik .env skonfigurowany
- [ ] Uprawnienia katalogów ustawione
- [ ] Strona logowania działa
- [ ] Logowanie jako admin działa
- [ ] Hasło administratora zmienione
- [ ] Upload zdjęć działa
- [ ] Wszystkie funkcje przetestowane

**🎉 Gratulacje! System jest gotowy do użycia!**
