# Instrukcja Instalacji - System Magazynowy

## Wymagania Systemowe

### Serwer
- **PHP**: 8.1 lub nowszy
- **MySQL**: 8.0 lub nowszy (lub MariaDB 10.4+)
- **Apache**: 2.4+ z mod_rewrite lub Nginx
- **Composer**: najnowsza wersja

### Rozszerzenia PHP
- pdo_mysql
- mbstring
- fileinfo
- gd lub imagick
- json
- openssl
- zip

## Instalacja

### 1. Przygotowanie środowiska

```bash
# Klonowanie/kopiowanie plików projektu
# Upewnij się, że wszystkie pliki są w katalogu głównym aplikacji

# Sprawdzenie wersji PHP
php -v

# Sprawdzenie rozszerzeń PHP
php -m | grep -E "(pdo_mysql|mbstring|fileinfo|gd|json)"
```

### 2. Instalacja zależności

```bash
# Instalacja Composer (jeśli nie jest zainstalowany)
# Pobierz z https://getcomposer.org/

# Instalacja zależności projektu
composer install --no-dev --optimize-autoloader
```

### 3. Konfiguracja bazy danych

```sql
-- Utworzenie bazy danych
CREATE DATABASE magazyn_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Utworzenie użytkownika (opcjonalnie)
CREATE USER 'magazyn_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON magazyn_system.* TO 'magazyn_user'@'localhost';
FLUSH PRIVILEGES;

-- Import struktury bazy danych
mysql -u root -p magazyn_system < database_schema.sql
```

### 4. Konfiguracja aplikacji

```bash
# Kopiowanie pliku konfiguracyjnego
cp .env.example .env

# Edycja konfiguracji
nano .env
```

**Przykładowa konfiguracja .env:**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
APP_NAME="System Magazynowy"

DB_HOST=localhost
DB_PORT=3306
DB_NAME=magazyn_system
DB_USERNAME=magazyn_user
DB_PASSWORD=your_strong_password

# Pozostałe ustawienia...
```

### 5. Ustawienia uprawnień

```bash
# Ustawienie uprawnień dla katalogów
chmod 755 public/
chmod -R 775 public/uploads/
chmod -R 775 logs/
chmod -R 775 cache/

# Właściciel plików (dla Apache)
chown -R www-data:www-data .
# lub dla Nginx
chown -R nginx:nginx .
```

### 6. Konfiguracja serwera web

#### Apache (.htaccess już skonfigurowany)
```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /path/to/project/public
    
    <Directory /path/to/project/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/magazyn_error.log
    CustomLog ${APACHE_LOG_DIR}/magazyn_access.log combined
</VirtualHost>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /path/to/project/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\. {
        deny all;
    }
}
```

### 7. Testowanie instalacji

```bash
# Sprawdzenie połączenia z bazą danych
php -r "
try {
    \$pdo = new PDO('mysql:host=localhost;dbname=magazyn_system', 'username', 'password');
    echo 'Połączenie z bazą danych: OK\n';
} catch (Exception \$e) {
    echo 'Błąd połączenia: ' . \$e->getMessage() . '\n';
}
"

# Sprawdzenie uprawnień do zapisu
touch public/uploads/test.txt && rm public/uploads/test.txt && echo "Uprawnienia do zapisu: OK"
```

## Konfiguracja Produkcyjna

### Bezpieczeństwo

1. **Zmień domyślne hasło administratora**
   - Zaloguj się jako admin/admin123
   - Zmień hasło w profilu użytkownika

2. **Konfiguracja HTTPS**
   ```apache
   # Przekierowanie HTTP na HTTPS
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

3. **Ukryj informacje o wersji PHP**
   ```ini
   expose_php = Off
   ```

4. **Konfiguracja firewall**
   - Zablokuj dostęp do portów innych niż 80/443
   - Ogranicz dostęp SSH

### Optymalizacja

1. **OPcache PHP**
   ```ini
   opcache.enable=1
   opcache.memory_consumption=256
   opcache.max_accelerated_files=20000
   opcache.validate_timestamps=0
   ```

2. **Kompresja GZIP** (już skonfigurowana w .htaccess)

3. **Cache bazy danych**
   ```ini
   query_cache_type = 1
   query_cache_size = 256M
   ```

### Backup

1. **Backup bazy danych**
   ```bash
   # Codziennie o 2:00
   0 2 * * * mysqldump -u username -p password magazyn_system > /backup/magazyn_$(date +\%Y\%m\%d).sql
   ```

2. **Backup plików**
   ```bash
   # Backup uploadowanych plików
   0 3 * * * tar -czf /backup/uploads_$(date +\%Y\%m\%d).tar.gz /path/to/project/public/uploads/
   ```

## Rozwiązywanie Problemów

### Błędy połączenia z bazą danych
- Sprawdź dane logowania w .env
- Upewnij się, że MySQL działa
- Sprawdź uprawnienia użytkownika

### Błędy uprawnień plików
```bash
# Przywrócenie uprawnień
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod -R 775 public/uploads/ logs/ cache/
```

### Błędy 500
- Sprawdź logi Apache/Nginx
- Włącz tryb debug w .env
- Sprawdź logi aplikacji w logs/

### Problemy z uploadem plików
- Sprawdź ustawienia PHP: upload_max_filesize, post_max_size
- Sprawdź uprawnienia katalogu uploads/
- Sprawdź dostępne miejsce na dysku

## Aktualizacja

1. **Backup danych**
2. **Pobranie nowej wersji**
3. **Aktualizacja zależności**: `composer install --no-dev`
4. **Migracja bazy danych** (jeśli potrzebna)
5. **Czyszczenie cache**
6. **Testowanie**

## Wsparcie

W przypadku problemów:
1. Sprawdź logi aplikacji
2. Sprawdź dokumentację
3. Skontaktuj się z administratorem systemu

---

**Uwaga**: Przed wdrożeniem w środowisku produkcyjnym przeprowadź dokładne testowanie wszystkich funkcjonalności.
