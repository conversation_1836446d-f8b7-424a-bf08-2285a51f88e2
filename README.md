# System Magazynowy dla Firmy Geodezyjnej

Nowoczesny webowy system zarządzania wypożyczaniem sprzętu geodezyjnego, komputerowego i biurowego.

## 🚀 Funkcjonalności

### 📦 Zarządzanie Przedmiotami
- ✅ Dodawanie, edycja i usuwanie przedmiotów
- ✅ Kategoryzacja (sprzęt geodezyjny, komputerowy, biurowy)
- ✅ Upload wielu zdjęć z automatycznymi miniaturami
- ✅ Wyszukiwanie i filtrowanie
- ✅ Zarządzanie statusami przedmiotów

### 📋 System Rezerwacji
- ✅ Składanie rezerwacji przez geodetów
- ✅ Zatwierdzanie/odrzucanie przez magazynierów
- ✅ Automatyczne zarządzanie statusami
- ✅ Powiadomienia i alerty

### 🔄 Wypożyczenia
- ✅ Wydawanie sprzętu z rejestracją
- ✅ Zwroty z kontrolą stanu
- ✅ Przedłużanie wypożyczeń
- ✅ Monitoring terminów zwrotu

### 🔀 Transfery Między Użytkownikami
- ✅ Prośby o przekazanie sprzętu
- ✅ Zatwierdzanie przez magazynierów
- ✅ Realizacja transferów
- ✅ Pełna historia transferów

### 📊 System Raportów
- ✅ Raport stanu magazynu
- ✅ Raporty wypożyczeń (globalne i personalne)
- ✅ Statystyki użytkowników
- ✅ Historia przedmiotów
- ✅ Eksport do CSV/PDF

### 👥 Zarządzanie Użytkownikami
- ✅ Role: Administrator/Magazynier, Geodeta
- ✅ System uprawnień
- ✅ Zarządzanie profilami
- ✅ Bezpieczne uwierzytelnianie

## 🛠 Technologie

### Backend
- **PHP 8.1+** - Nowoczesny PHP z OOP
- **MySQL 8.0+** - Baza danych z pełnym wsparciem
- **PDO** - Bezpieczne połączenia z bazą
- **Composer** - Zarządzanie zależnościami

### Frontend
- **Bootstrap 5** - Responsywny framework CSS
- **Alpine.js** - Lekki framework JavaScript
- **Vanilla JavaScript** - Dla zaawansowanych funkcji

### Bezpieczeństwo
- **bcrypt** - Hashowanie haseł
- **CSRF Protection** - Tokeny zabezpieczające
- **SQL Injection Prevention** - Prepared statements
- **XSS Protection** - Escape output
- **Session Management** - Bezpieczne sesje

## 📁 Struktura Projektu

```
/
├── config/              # Konfiguracja aplikacji
│   ├── bootstrap.php    # Inicjalizacja systemu
│   ├── config.php       # Główna konfiguracja
│   └── helpers.php      # Funkcje pomocnicze
├── src/                 # Kod źródłowy
│   ├── Controllers/     # Kontrolery MVC
│   ├── Models/          # Modele danych
│   ├── Services/        # Logika biznesowa
│   ├── Utils/           # Narzędzia pomocnicze
│   └── Database/        # Klasy bazy danych
├── public/              # Pliki publiczne
│   ├── assets/          # CSS, JS, obrazy
│   ├── uploads/         # Zdjęcia przedmiotów
│   └── index.php        # Punkt wejścia
├── templates/           # Szablony HTML
├── vendor/              # Zależności Composer
├── tests/               # Testy jednostkowe
├── database_schema.sql  # Struktura bazy danych
└── README.md           # Ten plik
```

## 🚀 Instalacja

### Wymagania
- PHP 8.1+
- MySQL 8.0+
- Apache/Nginx z mod_rewrite
- Composer

### Kroki instalacji

1. **Sklonuj/skopiuj projekt**
   ```bash
   # Skopiuj wszystkie pliki do katalogu serwera
   ```

2. **Zainstaluj zależności**
   ```bash
   composer install --no-dev --optimize-autoloader
   ```

3. **Skonfiguruj bazę danych**
   ```sql
   CREATE DATABASE magazyn_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   mysql -u root -p magazyn_system < database_schema.sql
   ```

4. **Skonfiguruj aplikację**
   ```bash
   cp .env.example .env
   # Edytuj .env z danymi bazy danych
   ```

5. **Ustaw uprawnienia**
   ```bash
   chmod -R 775 public/uploads/
   chmod -R 775 logs/
   chown -R www-data:www-data .
   ```

6. **Skonfiguruj serwer web**
   - Apache: Użyj dołączonego .htaccess
   - Nginx: Skonfiguruj według INSTALLATION.md

### Pierwsze logowanie
- **Użytkownik**: admin
- **Hasło**: admin123
- **⚠️ Zmień hasło po pierwszym logowaniu!**

## 📖 Dokumentacja

- **[INSTALLATION.md](INSTALLATION.md)** - Szczegółowa instrukcja instalacji
- **[USER_MANUAL.md](USER_MANUAL.md)** - Instrukcja użytkownika
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Dokumentacja architektury

## 🔧 Konfiguracja

### Główne ustawienia (.env)
```env
# Środowisko
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Baza danych
DB_HOST=localhost
DB_NAME=magazyn_system
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Upload plików
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp
```

## 🔒 Bezpieczeństwo

### Wbudowane zabezpieczenia
- Hashowanie haseł (bcrypt)
- Ochrona CSRF
- Walidacja danych wejściowych
- Escape output (XSS protection)
- Bezpieczne sesje
- Prepared statements (SQL injection)

### Zalecenia produkcyjne
- Używaj HTTPS
- Regularne aktualizacje
- Silne hasła
- Backup danych
- Monitoring logów

## 📊 Funkcjonalności Systemu

### Dashboard
- Statystyki w czasie rzeczywistym
- Alerty i powiadomienia
- Ostatnie aktywności
- Szybkie akcje

### Zarządzanie
- Przedmioty z galeriami zdjęć
- Kategorie z ustawieniami
- Użytkownicy z rolami
- Uprawnienia granularne

### Raporty
- Stan magazynu
- Historia wypożyczeń
- Statystyki użytkowników
- Eksport danych

## 🎯 Dla Kogo?

### Firmy Geodezyjne
- Zarządzanie drogim sprzętem geodezyjnym
- Kontrola wypożyczeń między projektami
- Monitoring wykorzystania sprzętu

### Biura Projektowe
- Sprzęt komputerowy i biurowy
- Zarządzanie zasobami
- Optymalizacja wykorzystania

### Inne Organizacje
- Dowolny sprzęt wypożyczalny
- Elastyczna konfiguracja
- Skalowalne rozwiązanie

## 🚀 Roadmap

### Planowane funkcjonalności
- [ ] Powiadomienia email/SMS
- [ ] API REST
- [ ] Aplikacja mobilna
- [ ] Integracje zewnętrzne
- [ ] Zaawansowane raporty
- [ ] Workflow automation

## 🤝 Wsparcie

### Problemy techniczne
1. Sprawdź logi aplikacji
2. Przeczytaj dokumentację
3. Skontaktuj się z administratorem

### Rozwój
System jest gotowy do rozszerzeń i dostosowań według potrzeb organizacji.

## 📄 Licencja

Ten projekt został stworzony dla firmy geodezyjnej. Wszystkie prawa zastrzeżone.

---

**Wersja**: 1.0  
**Data**: <?= date('Y-m-d') ?>  
**Autor**: System Magazynowy Team

## 🎉 Gotowe do użycia!

System jest w pełni funkcjonalny i gotowy do wdrożenia w środowisku produkcyjnym. Zawiera wszystkie wymagane funkcjonalności oraz dodatkowe usprawnienia dla lepszego doświadczenia użytkownika.
