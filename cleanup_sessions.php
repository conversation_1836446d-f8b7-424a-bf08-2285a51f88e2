<?php
/**
 * Skrypt do czyszczenia problematycznych sesji
 */

// Bootstrap aplikacji
require_once __DIR__ . '/config/bootstrap.php';

try {
    echo "🧹 Czyszczenie sesji użytkownika 'pkania'...\n";
    
    // Znajdź ID użytkownika pkania
    $user = DB->selectOne("SELECT id FROM users WHERE username = 'pkania'");
    
    if (!$user) {
        echo "❌ Użytkownik 'pkania' nie został znaleziony\n";
        exit(1);
    }
    
    $userId = $user['id'];
    echo "✅ Znaleziono użytkownika pkania (ID: {$userId})\n";
    
    // Usuń wszystkie sesje tego użytkownika
    $deletedSessions = DB->delete("DELETE FROM user_sessions WHERE user_id = :user_id", ['user_id' => $userId]);
    
    echo "🗑️ Usunięto {$deletedSessions} sesji użytkownika pkania\n";
    
    // Wyczyść też wygasłe sesje wszystkich użytkowników
    $expiredSessions = DB->delete("DELETE FROM user_sessions WHERE expires_at < NOW()");
    
    echo "🗑️ Usunięto {$expiredSessions} wygasłych sesji\n";
    
    echo "✅ Czyszczenie zakończone pomyślnie!\n";
    echo "💡 Teraz możesz spróbować zalogować się ponownie jako pkania\n";
    
} catch (Exception $e) {
    echo "❌ Błąd: " . $e->getMessage() . "\n";
    exit(1);
}
