<?php

/**
 * Bootstrap aplikacji - inicjalizacja systemu
 */

// Sprawdzenie wersji PHP
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die('System wymaga PHP 7.4 lub nowszego. Aktualna wersja: ' . PHP_VERSION);
}

// Ustawienie raportowania błędów
error_reporting(E_ALL);

// Autoloader Composer
require_once __DIR__ . '/../vendor/autoload.php';

// Inicjalizacja routera
use App\Utils\Router;
$router = new Router();

// Ładowanie konfiguracji
$config = require_once __DIR__ . '/config.php';

// Ustawienie strefy czasowej
date_default_timezone_set($config['app']['timezone']);

// Konfiguracja wyświetlania błędów
ini_set('display_errors', $config['app']['debug'] ? '1' : '0');
ini_set('display_startup_errors', $config['app']['debug'] ? '1' : '0');

// Konfiguracja sesji
ini_set('session.name', $config['session']['name']);
ini_set('session.gc_maxlifetime', $config['session']['lifetime']);
ini_set('session.cookie_lifetime', $config['session']['lifetime']);
ini_set('session.cookie_path', $config['session']['path']);
ini_set('session.cookie_domain', $config['session']['domain']);
ini_set('session.cookie_secure', $config['session']['secure'] ? '1' : '0');
ini_set('session.cookie_httponly', $config['session']['httponly'] ? '1' : '0');
ini_set('session.cookie_samesite', $config['session']['samesite']);
ini_set('session.use_strict_mode', '1');

// Konfiguracja upload plików
ini_set('upload_max_filesize', $config['upload']['max_size']);
ini_set('post_max_size', $config['upload']['max_size'] * 2);
ini_set('max_file_uploads', '10');

// Zabezpieczenia HTTP headers
if (!headers_sent()) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    if ($config['session']['secure']) {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

// Globalne zmienne konfiguracyjne
define('APP_ROOT', dirname(__DIR__));
define('PUBLIC_ROOT', APP_ROOT);
define('UPLOAD_PATH', APP_ROOT . '/' . $config['upload']['path']);
define('TEMPLATES_PATH', APP_ROOT . '/templates');
define('CONFIG', $config);

// Utworzenie katalogów jeśli nie istnieją
$directories = [
    UPLOAD_PATH,
    UPLOAD_PATH . '/items',
    UPLOAD_PATH . '/thumbnails',
    APP_ROOT . '/logs',
    APP_ROOT . '/cache',
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Funkcje pomocnicze
require_once __DIR__ . '/helpers.php';

// Inicjalizacja bazy danych
try {
    $database = new App\Database\Database($config['database']);
    define('DB', $database);
} catch (Exception $e) {
    if ($config['app']['debug']) {
        die('Błąd połączenia z bazą danych: ' . $e->getMessage());
    } else {
        die('Błąd połączenia z bazą danych. Skontaktuj się z administratorem.');
    }
}

// Inicjalizacja sesji
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Regeneracja ID sesji dla bezpieczeństwa
if (!isset($_SESSION['regenerated']) || $_SESSION['regenerated'] < time() - 300) {
    session_regenerate_id(true);
    $_SESSION['regenerated'] = time();
}

// Sprawdzenie CSRF token dla POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['ajax'])) {
    if (!isset($_POST[$config['security']['csrf_token_name']]) || 
        !hash_equals($_SESSION['csrf_token'] ?? '', $_POST[$config['security']['csrf_token_name']] ?? '')) {
        http_response_code(403);
        die('Nieprawidłowy token CSRF');
    }
}

// Generowanie nowego CSRF token jeśli nie istnieje
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Inicjalizacja routingu
$router = new App\Utils\Router();

// Middleware dla uwierzytelniania
$router->addMiddleware(function($request, $response, $next) {
    $authController = new App\Controllers\AuthController();

    // Sprawdzenie czy użytkownik jest zalogowany dla chronionych tras
    $protectedRoutes = ['/dashboard', '/items', '/loans', '/reports', '/admin'];
    $currentPath = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

    // Sprawdź czy to chroniona trasa
    $isProtectedRoute = false;
    foreach ($protectedRoutes as $route) {
        if (strpos($currentPath, $route) === 0) {
            $isProtectedRoute = true;
            break;
        }
    }

    // Jeśli to chroniona trasa i użytkownik nie jest zalogowany
    if ($isProtectedRoute && !$authController->isLoggedIn()) {
        // Unikaj przekierowania jeśli już jesteśmy na stronie logowania
        if ($currentPath !== '/login') {
            header('Location: /login');
            exit;
        }
    }

    return $next($request, $response);
});

return $router;
