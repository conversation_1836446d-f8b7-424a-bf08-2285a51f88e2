<?php

/**
 * Główny plik konfiguracyjny systemu magazynowego
 */

// Ładowanie zmiennych środowiskowych (bez Dotenv)
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if (strpos($line, '=') !== false && substr($line, 0, 1) !== '#') {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Konfiguracja aplikacji
return [
    'app' => [
        'name' => $_ENV['APP_NAME'] ?? 'System Magazynowy',
        'env' => $_ENV['APP_ENV'] ?? 'production',
        'debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
        'url' => $_ENV['APP_URL'] ?? 'http://localhost',
        'timezone' => $_ENV['TIMEZONE'] ?? 'Europe/Warsaw',
    ],

    'database' => [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'port' => $_ENV['DB_PORT'] ?? 3306,
        'name' => $_ENV['DB_NAME'] ?? 'magazyn_system',
        'username' => $_ENV['DB_USERNAME'] ?? 'root',
        'password' => $_ENV['DB_PASSWORD'] ?? '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ],
    ],

    'session' => [
        'name' => $_ENV['SESSION_NAME'] ?? 'MAGAZYN_SESSION',
        'lifetime' => (int)($_ENV['SESSION_LIFETIME'] ?? 7200),
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Strict',
    ],

    'security' => [
        'csrf_token_name' => $_ENV['CSRF_TOKEN_NAME'] ?? 'csrf_token',
        'password_min_length' => (int)($_ENV['PASSWORD_MIN_LENGTH'] ?? 8),
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minut
    ],

    'upload' => [
        'max_size' => (int)($_ENV['UPLOAD_MAX_SIZE'] ?? 10485760), // 10MB
        'allowed_types' => explode(',', $_ENV['UPLOAD_ALLOWED_TYPES'] ?? 'jpg,jpeg,png,gif,webp'),
        'path' => $_ENV['UPLOAD_PATH'] ?? 'uploads',
        'thumbnails' => [
            'small' => ['width' => 150, 'height' => 150],
            'medium' => ['width' => 400, 'height' => 400],
            'large' => ['width' => 800, 'height' => 600],
        ],
    ],

    'mail' => [
        'host' => $_ENV['MAIL_HOST'] ?? 'localhost',
        'port' => (int)($_ENV['MAIL_PORT'] ?? 587),
        'username' => $_ENV['MAIL_USERNAME'] ?? '',
        'password' => $_ENV['MAIL_PASSWORD'] ?? '',
        'encryption' => 'tls',
        'from' => [
            'address' => $_ENV['MAIL_FROM_ADDRESS'] ?? 'noreply@localhost',
            'name' => $_ENV['MAIL_FROM_NAME'] ?? 'System Magazynowy',
        ],
    ],

    'pagination' => [
        'reports_per_page' => (int)($_ENV['REPORTS_PER_PAGE'] ?? 50),
        'items_per_page' => (int)($_ENV['ITEMS_PER_PAGE'] ?? 20),
        'max_per_page' => 100,
    ],

    'cache' => [
        'enabled' => filter_var($_ENV['CACHE_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'lifetime' => (int)($_ENV['CACHE_LIFETIME'] ?? 3600),
        'prefix' => 'magazyn_',
    ],

    'logging' => [
        'enabled' => filter_var($_ENV['LOG_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
        'level' => $_ENV['LOG_LEVEL'] ?? 'error',
        'path' => $_ENV['LOG_PATH'] ?? 'logs/',
        'max_files' => 30,
    ],

    'features' => [
        'email_notifications' => true,
        'auto_return_reminders' => true,
        'transfer_approvals' => true,
        'maintenance_mode' => false,
        'api_enabled' => false,
    ],

    'roles' => [
        'admin' => [
            'name' => 'Administrator/Magazynier',
            'permissions' => [
                'manage_items',
                'manage_users',
                'approve_loans',
                'approve_transfers',
                'view_all_reports',
                'system_settings',
            ],
        ],
        'geodeta' => [
            'name' => 'Geodeta',
            'permissions' => [
                'view_items',
                'make_reservations',
                'request_transfers',
                'view_own_loans',
                'view_own_reports',
            ],
        ],
    ],

    'item_statuses' => [
        'available' => 'Dostępny',
        'reserved' => 'Zarezerwowany',
        'lent' => 'Wypożyczony',
        'maintenance' => 'W serwisie',
    ],

    'loan_statuses' => [
        'active' => 'Aktywne',
        'returned' => 'Zwrócone',
        'overdue' => 'Przeterminowane',
    ],

    'reservation_statuses' => [
        'pending' => 'Oczekująca',
        'approved' => 'Zatwierdzona',
        'rejected' => 'Odrzucona',
        'cancelled' => 'Anulowana',
        'fulfilled' => 'Zrealizowana',
    ],

    'transfer_statuses' => [
        'pending' => 'Oczekuje',
        'approved' => 'Zatwierdzone',
        'rejected' => 'Odrzucone',
        'completed' => 'Zakończone',
    ],
];
