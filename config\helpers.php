<?php

/**
 * Funkcje pomocnicze dostępne globalnie w aplikacji
 */

/**
 * Escape HTML output dla bezpieczeństwa
 */
function e($value, $doubleEncode = true) {
    return htmlspecialchars($value ?? '', ENT_QUOTES, 'UTF-8', $doubleEncode);
}

/**
 * Sprawdzenie czy wartoś<PERSON> jest pusta
 */
function isEmpty($value) {
    return empty($value) && $value !== '0' && $value !== 0;
}

/**
 * Formatowanie daty
 */
function formatDate($date, $format = 'd.m.Y') {
    if (empty($date)) return '';
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

/**
 * Formatowanie daty i czasu
 */
function formatDateTime($datetime, $format = 'd.m.Y H:i') {
    return formatDate($datetime, $format);
}

/**
 * Formatowanie rozmiaru pliku
 */
function formatFileSize($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Generowanie URL
 */
function url($path = '') {
    $baseUrl = rtrim(CONFIG['app']['url'], '/');
    $path = ltrim($path, '/');
    
    return $baseUrl . ($path ? '/' . $path : '');
}

/**
 * Generowanie URL do zasobów
 */
function asset($path) {
    return url('assets/' . ltrim($path, '/'));
}

/**
 * Generowanie URL do uploadowanych plików
 */
function uploadUrl($path) {
    return url('uploads/' . ltrim($path, '/'));
}

/**
 * Przekierowanie
 */
function redirect($url, $statusCode = 302) {
    if (!headers_sent()) {
        header('Location: ' . $url, true, $statusCode);
        exit;
    }
}



/**
 * Sprawdzenie czy request jest AJAX
 */
function isAjax() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Zwrócenie odpowiedzi JSON
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}





/**
 * Generowanie CSRF token
 */
function csrfToken() {
    return $_SESSION['csrf_token'] ?? '';
}

/**
 * Generowanie pola CSRF dla formularzy
 */
function csrfField() {
    $tokenName = CONFIG['security']['csrf_token_name'];
    $token = csrfToken();
    
    return "<input type=\"hidden\" name=\"{$tokenName}\" value=\"{$token}\">";
}

/**
 * Dodanie wiadomości flash
 */
function flash($type, $message) {
    if (!isset($_SESSION['flash'])) {
        $_SESSION['flash'] = [];
    }
    
    $_SESSION['flash'][] = [
        'type' => $type,
        'message' => $message
    ];
}

/**
 * Pobranie i usunięcie wiadomości flash
 */
function getFlashMessages() {
    $messages = $_SESSION['flash'] ?? [];
    unset($_SESSION['flash']);
    
    return $messages;
}

/**
 * Walidacja email
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generowanie bezpiecznej nazwy pliku
 */
function sanitizeFilename($filename) {
    // Usunięcie rozszerzenia
    $extension = pathinfo($filename, PATHINFO_EXTENSION);
    $name = pathinfo($filename, PATHINFO_FILENAME);
    
    // Sanityzacja nazwy
    $name = preg_replace('/[^a-zA-Z0-9_-]/', '_', $name);
    $name = preg_replace('/_+/', '_', $name);
    $name = trim($name, '_');
    
    // Jeśli nazwa jest pusta, użyj timestamp
    if (empty($name)) {
        $name = 'file_' . time();
    }
    
    return $name . ($extension ? '.' . $extension : '');
}

/**
 * Generowanie unikalnej nazwy pliku
 */
function generateUniqueFilename($originalName, $directory) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $name = pathinfo($originalName, PATHINFO_FILENAME);
    $name = sanitizeFilename($name);
    
    $counter = 0;
    $filename = $name . '.' . $extension;
    
    while (file_exists($directory . '/' . $filename)) {
        $counter++;
        $filename = $name . '_' . $counter . '.' . $extension;
    }
    
    return $filename;
}

/**
 * Logowanie błędów
 */
function logError($message, $context = []) {
    if (!CONFIG['logging']['enabled']) {
        return;
    }
    
    $logFile = APP_ROOT . '/' . CONFIG['logging']['path'] . 'error_' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    
    $logMessage = "[{$timestamp}] ERROR: {$message}{$contextStr}" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Debug - wyświetlenie zmiennej (tylko w trybie debug)
 */
function dd($var) {
    if (CONFIG['app']['debug']) {
        echo '<pre>';
        var_dump($var);
        echo '</pre>';
        exit;
    }
}

/**
 * Paginacja
 */
function paginate($totalItems, $currentPage, $itemsPerPage, $baseUrl) {
    $totalPages = ceil($totalItems / $itemsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));

    return [
        'current_page' => $currentPage,
        'total_pages' => $totalPages,
        'total_items' => $totalItems,
        'items_per_page' => $itemsPerPage,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'previous_url' => $currentPage > 1 ? $baseUrl . '?page=' . ($currentPage - 1) : null,
        'next_url' => $currentPage < $totalPages ? $baseUrl . '?page=' . ($currentPage + 1) : null,
        'offset' => ($currentPage - 1) * $itemsPerPage,
        'start_page' => max(1, $currentPage - 2),
        'end_page' => min($totalPages, $currentPage + 2)
    ];
}

/**
 * Sprawdzenie czy użytkownik ma uprawnienie
 */
function hasPermission(string $permission): bool
{
    if (!isLoggedIn()) {
        return false;
    }

    $user = currentUser();
    $userRole = $user['role'] ?? 'geodeta';

    $permissions = CONFIG['roles'][$userRole]['permissions'] ?? [];

    return in_array($permission, $permissions);
}

/**
 * Sprawdzenie czy użytkownik ma rolę
 */
function hasRole(string $role): bool
{
    if (!isLoggedIn()) {
        return false;
    }

    $user = currentUser();
    return ($user['role'] ?? '') === $role;
}

/**
 * Pobranie koloru dla statusu
 */
function getStatusColor(string $status): string
{
    $colors = [
        'available' => 'success',
        'reserved' => 'warning',
        'lent' => 'info',
        'maintenance' => 'danger',
        'pending' => 'warning',
        'approved' => 'success',
        'rejected' => 'danger',
        'cancelled' => 'secondary',
        'active' => 'success',
        'returned' => 'secondary',
        'overdue' => 'danger',
        'fulfilled' => 'primary'
    ];

    return $colors[$status] ?? 'secondary';
}

/**
 * Pobranie nazwy statusu
 */
function getStatusName(string $status): string
{
    $names = [
        'available' => 'Dostępny',
        'reserved' => 'Zarezerwowany',
        'lent' => 'Wypożyczony',
        'maintenance' => 'W serwisie',
        'pending' => 'Oczekujące',
        'approved' => 'Zatwierdzone',
        'rejected' => 'Odrzucone',
        'cancelled' => 'Anulowane',
        'active' => 'Aktywne',
        'returned' => 'Zwrócone',
        'overdue' => 'Przeterminowane',
        'fulfilled' => 'Zrealizowane'
    ];

    return $names[$status] ?? ucfirst($status);
}

/**
 * Funkcja back() - powrót do poprzedniej strony
 */
function back(): void
{
    $referer = $_SERVER['HTTP_REFERER'] ?? '/dashboard';
    redirect($referer);
}

/**
 * Sprawdzenie czy użytkownik jest zalogowany
 */
function isLoggedIn(): bool
{
    // Używamy AuthController dla spójności
    static $authController = null;
    if ($authController === null) {
        $authController = new App\Controllers\AuthController();
    }

    return $authController->isLoggedIn();
}

/**
 * Pobranie danych aktualnego użytkownika
 */
function currentUser(): ?array
{
    return $_SESSION['user'] ?? null;
}


