-- System Magazynowy dla Firmy Geodezyjnej
-- Struktura bazy danych z tabelami i indeksami

-- <PERSON>bela użytkowników
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    role ENUM('admin', 'geodeta') NOT NULL DEFAULT 'geodeta',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> ka<PERSON><PERSON><PERSON> przedmiotów
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_lendable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> przedmiotów
CREATE TABLE items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    details TEXT,
    category_id INT NOT NULL,
    is_lendable BOOLEAN DEFAULT TRUE,
    status ENUM('available', 'reserved', 'lent', 'maintenance') DEFAULT 'available',
    current_holder_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (current_holder_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabela zdjęć przedmiotów
CREATE TABLE item_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    file_size INT,
    mime_type VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);

-- Tabela rezerwacji
CREATE TABLE reservations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'cancelled', 'fulfilled') DEFAULT 'pending',
    requested_date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabela wypożyczeń
CREATE TABLE loans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    borrower_id INT NOT NULL,
    lender_id INT NOT NULL, -- magazynier wydający
    reservation_id INT NULL,
    loan_date DATE NOT NULL,
    expected_return_date DATE,
    actual_return_date DATE NULL,
    return_receiver_id INT NULL, -- magazynier przyjmujący zwrot
    status ENUM('active', 'returned', 'overdue') DEFAULT 'active',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
    FOREIGN KEY (borrower_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (lender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (return_receiver_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE SET NULL
);

-- Tabela próśb o przekazanie
CREATE TABLE transfer_requests (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    requester_id INT NOT NULL, -- geodeta proszący
    current_holder_id INT NOT NULL, -- obecny posiadacz
    approver_id INT NULL, -- magazynier zatwierdzający
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    message TEXT,
    approved_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
    FOREIGN KEY (requester_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (current_holder_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Tabela transferów (historia przekazań)
CREATE TABLE transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    from_user_id INT NULL, -- NULL jeśli z magazynu
    to_user_id INT NULL, -- NULL jeśli do magazynu
    transfer_type ENUM('loan', 'return', 'transfer', 'maintenance') NOT NULL,
    processed_by_id INT NOT NULL, -- magazynier realizujący
    transfer_request_id INT NULL,
    loan_id INT NULL,
    transfer_date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (processed_by_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (transfer_request_id) REFERENCES transfer_requests(id) ON DELETE SET NULL,
    FOREIGN KEY (loan_id) REFERENCES loans(id) ON DELETE SET NULL
);

-- Tabela sesji użytkowników
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- INDEKSY dla optymalizacji wydajności

-- Indeksy dla tabeli users
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Indeksy dla tabeli items
CREATE INDEX idx_items_category ON items(category_id);
CREATE INDEX idx_items_status ON items(status);
CREATE INDEX idx_items_holder ON items(current_holder_id);
CREATE INDEX idx_items_name ON items(name);
CREATE INDEX idx_items_lendable ON items(is_lendable);

-- Indeksy dla tabeli item_images
CREATE INDEX idx_images_item ON item_images(item_id);
CREATE INDEX idx_images_primary ON item_images(is_primary);

-- Indeksy dla tabeli reservations
CREATE INDEX idx_reservations_item ON reservations(item_id);
CREATE INDEX idx_reservations_user ON reservations(user_id);
CREATE INDEX idx_reservations_status ON reservations(status);
CREATE INDEX idx_reservations_date ON reservations(requested_date);

-- Indeksy dla tabeli loans
CREATE INDEX idx_loans_item ON loans(item_id);
CREATE INDEX idx_loans_borrower ON loans(borrower_id);
CREATE INDEX idx_loans_lender ON loans(lender_id);
CREATE INDEX idx_loans_status ON loans(status);
CREATE INDEX idx_loans_date ON loans(loan_date);
CREATE INDEX idx_loans_return_date ON loans(actual_return_date);

-- Indeksy dla tabeli transfer_requests
CREATE INDEX idx_transfer_requests_item ON transfer_requests(item_id);
CREATE INDEX idx_transfer_requests_requester ON transfer_requests(requester_id);
CREATE INDEX idx_transfer_requests_holder ON transfer_requests(current_holder_id);
CREATE INDEX idx_transfer_requests_status ON transfer_requests(status);

-- Indeksy dla tabeli transfers
CREATE INDEX idx_transfers_item ON transfers(item_id);
CREATE INDEX idx_transfers_from_user ON transfers(from_user_id);
CREATE INDEX idx_transfers_to_user ON transfers(to_user_id);
CREATE INDEX idx_transfers_type ON transfers(transfer_type);
CREATE INDEX idx_transfers_date ON transfers(transfer_date);
CREATE INDEX idx_transfers_processed_by ON transfers(processed_by_id);

-- Indeksy dla tabeli user_sessions
CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires ON user_sessions(expires_at);

-- Indeksy dla tabeli categories
CREATE INDEX idx_categories_lendable ON categories(is_lendable);

-- Wstawienie podstawowych danych

-- Kategorie domyślne
INSERT INTO categories (name, description, is_lendable) VALUES
('Sprzęt geodezyjny', 'Instrumenty i urządzenia geodezyjne', TRUE),
('Sprzęt komputerowy', 'Laptopy, tablety, drukarki', TRUE),
('Sprzęt biurowy', 'Artykuły biurowe i materiały eksploatacyjne', FALSE);

-- Użytkownik administratora (hasło: admin123 - należy zmienić!)
INSERT INTO users (username, email, password_hash, first_name, last_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'Systemu', 'admin');
