<?php
/**
 * Debug uploadów - sprawdza gdzie są zdjęcia
 */

echo "<h1>🔍 Debug lokalizacji zd<PERSON></h1>";
echo "<style>body{font-family:Arial;margin:20px;} .path{background:#f5f5f5;padding:10px;border-radius:5px;font-family:monospace;} .success{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// Załaduj konfigurację
require_once 'config/bootstrap.php';

echo "<h2>📁 Sprawdzanie ścieżek uploadów</h2>";

// 1. Sprawdź konfigurację
echo "<h3>⚙️ Konfiguracja:</h3>";
echo "<p><strong>UPLOAD_PATH:</strong> <span class='path'>" . UPLOAD_PATH . "</span></p>";
echo "<p><strong>APP_ROOT:</strong> <span class='path'>" . APP_ROOT . "</span></p>";
echo "<p><strong>PUBLIC_ROOT:</strong> <span class='path'>" . PUBLIC_ROOT . "</span></p>";

// 2. Sprawdź czy katalogi istnieją
echo "<h3>📂 Sprawdzanie katalogów:</h3>";

$directories = [
    'UPLOAD_PATH' => UPLOAD_PATH,
    'UPLOAD_PATH/items' => UPLOAD_PATH . '/items',
    'UPLOAD_PATH/thumbnails' => UPLOAD_PATH . '/thumbnails',
    'public/uploads' => APP_ROOT . '/public/uploads',
    'public/uploads/items' => APP_ROOT . '/public/uploads/items',
    'public/uploads/thumbnails' => APP_ROOT . '/public/uploads/thumbnails',
    'uploads' => APP_ROOT . '/uploads',
    'uploads/items' => APP_ROOT . '/uploads/items',
];

foreach ($directories as $name => $path) {
    echo "<p><strong>{$name}:</strong> <span class='path'>{$path}</span> ";
    if (is_dir($path)) {
        echo "<span class='success'>✅ ISTNIEJE</span>";
        
        // Sprawdź pliki w katalogu
        $files = glob($path . '/*');
        if (!empty($files)) {
            echo " <span class='warning'>📁 " . count($files) . " plików</span>";
        }
    } else {
        echo "<span class='error'>❌ NIE ISTNIEJE</span>";
    }
    echo "</p>";
}

// 3. Sprawdź bazę danych
echo "<h3>🗄️ Zdjęcia w bazie danych:</h3>";

try {
    $pdo = new PDO(
        "mysql:host=" . CONFIG['database']['host'] . ";dbname=" . CONFIG['database']['name'] . ";charset=utf8mb4",
        CONFIG['database']['username'],
        CONFIG['database']['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $pdo->query("SELECT id, item_id, filename, original_name, created_at FROM item_images ORDER BY created_at DESC LIMIT 10");
    $images = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($images)) {
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
        echo "<tr><th>ID</th><th>Item ID</th><th>Filename</th><th>Original Name</th><th>Data</th><th>Plik istnieje?</th></tr>";
        
        foreach ($images as $image) {
            echo "<tr>";
            echo "<td>{$image['id']}</td>";
            echo "<td>{$image['item_id']}</td>";
            echo "<td style='font-family:monospace;'>{$image['filename']}</td>";
            echo "<td>{$image['original_name']}</td>";
            echo "<td>{$image['created_at']}</td>";
            
            // Sprawdź czy plik istnieje w różnych lokalizacjach
            $possiblePaths = [
                UPLOAD_PATH . '/items/' . $image['filename'],
                APP_ROOT . '/public/uploads/items/' . $image['filename'],
                APP_ROOT . '/uploads/items/' . $image['filename'],
                APP_ROOT . '/items/' . $image['filename'],
            ];
            
            $found = false;
            foreach ($possiblePaths as $path) {
                if (file_exists($path)) {
                    echo "<td class='success'>✅ {$path}</td>";
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                echo "<td class='error'>❌ BRAK PLIKU</td>";
            }
            
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='warning'>⚠️ Brak zdjęć w bazie danych</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Błąd bazy danych: " . $e->getMessage() . "</p>";
}

// 4. Sprawdź wszystkie możliwe lokalizacje plików
echo "<h3>🔍 Szukanie plików zdjęć w całym systemie:</h3>";

$searchPaths = [
    APP_ROOT,
    APP_ROOT . '/public',
    APP_ROOT . '/uploads',
    dirname(APP_ROOT),
];

foreach ($searchPaths as $searchPath) {
    if (is_dir($searchPath)) {
        echo "<h4>Szukanie w: <span class='path'>{$searchPath}</span></h4>";
        
        // Znajdź pliki .jpg, .png, .gif
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($searchPath));
        $imageFiles = [];
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $extension = strtolower($file->getExtension());
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $imageFiles[] = $file->getPathname();
                }
            }
        }
        
        if (!empty($imageFiles)) {
            echo "<ul>";
            foreach (array_slice($imageFiles, 0, 20) as $file) { // Pokaż tylko pierwsze 20
                echo "<li><span class='path'>{$file}</span></li>";
            }
            if (count($imageFiles) > 20) {
                echo "<li><em>... i " . (count($imageFiles) - 20) . " więcej plików</em></li>";
            }
            echo "</ul>";
        } else {
            echo "<p>Brak plików obrazów</p>";
        }
    }
}

// 5. Sprawdź uprawnienia
echo "<h3>🔐 Uprawnienia katalogów:</h3>";

$checkPaths = [UPLOAD_PATH, UPLOAD_PATH . '/items', UPLOAD_PATH . '/thumbnails'];

foreach ($checkPaths as $path) {
    if (is_dir($path)) {
        $perms = fileperms($path);
        $permsOctal = substr(sprintf('%o', $perms), -4);
        echo "<p><span class='path'>{$path}</span> - uprawnienia: <strong>{$permsOctal}</strong>";
        
        if (is_writable($path)) {
            echo " <span class='success'>✅ ZAPISYWALNY</span>";
        } else {
            echo " <span class='error'>❌ BRAK ZAPISU</span>";
        }
        echo "</p>";
    }
}

echo "<h3>💡 Instrukcje:</h3>";
echo "<ol>";
echo "<li>Sprawdź powyższe ścieżki na serwerze FTP</li>";
echo "<li>Jeśli pliki są w innej lokalizacji - skopiuj je do właściwego miejsca</li>";
echo "<li>Sprawdź uprawnienia katalogów (powinny być 755)</li>";
echo "<li>Sprawdź czy serwer web ma dostęp do plików</li>";
echo "</ol>";
?>
