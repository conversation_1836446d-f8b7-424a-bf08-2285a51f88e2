-- Naprawa logowania administratora
-- Wykonaj ten plik w phpMyAdmin jeśli nie możesz się zalogować

-- Sprawdzenie czy użytkownik admin istnieje
SELECT * FROM users WHERE username = 'admin';

-- <PERSON><PERSON><PERSON> użytkownik admin nie istnieje, utw<PERSON><PERSON> go:
INSERT IGNORE INTO users (username, email, password_hash, first_name, last_name, role, is_active, created_at) VALUES
('admin', '<EMAIL>', '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', 'Administrator', 'Systemu', 'admin', 1, NOW());

-- Je<PERSON><PERSON> użytkownik admin istnieje, ale hasło nie działa, zaktualizuj hasło:
UPDATE users SET 
    password_hash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm',
    is_active = 1,
    role = 'admin'
WHERE username = 'admin';

-- Sprawdzenie po aktualizacji
SELECT username, email, role, is_active, created_at FROM users WHERE username = 'admin';

-- DANE DO LOGOWANIA:
-- Użytkownik: admin
-- Hasło: admin123
