<?php
/**
 * Skrypt do naprawy hasła administratora
 */

// Generowanie nowego hasha dla hasła admin123
$password = 'admin123';
$hash = password_hash($password, PASSWORD_DEFAULT);

echo "Nowy hash dla hasła 'admin123':\n";
echo $hash . "\n\n";

// Sprawdzenie czy hash działa
if (password_verify($password, $hash)) {
    echo "✅ Hash jest poprawny!\n";
} else {
    echo "❌ Hash jest błędny!\n";
}

// SQL do aktualizacji hasła w bazie
echo "\n--- SQL do wykonania w phpMyAdmin ---\n";
echo "UPDATE users SET password_hash = '{$hash}' WHERE username = 'admin';\n";
?>
