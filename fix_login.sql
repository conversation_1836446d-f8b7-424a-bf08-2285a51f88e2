-- NAPRAWIENIE SYSTEMU LOGOWANIA

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON> tabel<PERSON> login_attempts
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    attempts INT DEFAULT 1,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address)
);

-- 2. <PERSON><PERSON><PERSON> wszystkie blokady dla admin
DELETE FROM login_attempts WHERE username = 'admin';

-- 3. Ustaw POPRAWNY hash hasła dla admin123
-- Ten hash został wygenerowany przez PHP: password_hash('admin123', PASSWORD_DEFAULT)
UPDATE users SET 
    password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    is_active = 1,
    role = 'admin',
    updated_at = NOW()
WHERE username = 'admin';

-- 4. Sprawdzenie czy wszystko jest OK
SELECT 
    username, 
    email, 
    role, 
    is_active,
    SUBSTRING(password_hash, 1, 20) as password_hash_start
FROM users 
WHERE username = 'admin';

-- DANE DO LOGOWANIA:
-- Username: admin
-- Password: admin123
