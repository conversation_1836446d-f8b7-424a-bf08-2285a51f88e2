<?php
/**
 * Generator hashów haseł
 */

echo "<h1>🔐 Generator hashów haseł</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .hash{background:#f5f5f5;padding:10px;border-radius:5px;font-family:monospace;word-break:break-all;} .success{color:green;} .info{color:blue;}</style>";

// Hasło do zahashowania
$password = 'password123';
$username = 'j<PERSON><PERSON><PERSON>';

echo "<h2>🧪 Generowanie hashów dla hasła: <strong>{$password}</strong></h2>";
echo "<h3>👤 Użytkownik: <strong>{$username}</strong></h3>";

// Wygeneruj 5 różnych hashów tego samego hasła
echo "<h3>📋 Wygenerowane hashe (każdy jest unikalny!):</h3>";

for ($i = 1; $i <= 5; $i++) {
    $hash = password_hash($password, PASSWORD_DEFAULT);
    echo "<h4>Hash #{$i}:</h4>";
    echo "<div class='hash'>{$hash}</div>";
    
    // Sprawdź czy hash działa
    if (password_verify($password, $hash)) {
        echo "<p class='success'>✅ Hash jest poprawny!</p>";
    } else {
        echo "<p class='error'>❌ Hash jest błędny!</p>";
    }
    
    echo "<h5>SQL do wykonania:</h5>";
    echo "<div class='hash'>UPDATE users SET password_hash = '{$hash}' WHERE username = '{$username}';</div>";
    echo "<hr>";
}

echo "<h3>🔍 Dlaczego każdy hash jest inny?</h3>";
echo "<ul>";
echo "<li><strong>Salt</strong> - każdy hash ma unikalną sól</li>";
echo "<li><strong>Bezpieczeństwo</strong> - uniemożliwia ataki rainbow table</li>";
echo "<li><strong>password_verify()</strong> - automatycznie wyciąga sól z hasha</li>";
echo "</ul>";

echo "<h3>💡 Wybierz DOWOLNY z powyższych hashów - wszystkie działają!</h3>";
?>
