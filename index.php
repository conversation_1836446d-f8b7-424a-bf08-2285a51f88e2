<?php

/**
 * <PERSON>t wej<PERSON>cia aplikacji - System Magazynowy
 */

// Bootstrap aplikacji
$router = require_once __DIR__ . '/config/bootstrap.php';

// Definicja tras

// Strona główna
$router->get('/', function() {
    if (isLoggedIn()) {
        redirect('/dashboard');
    } else {
        redirect('/login');
    }
});

// Uwierzytelnianie
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/logout', 'AuthController@logout');

// Dashboard
$router->get('/dashboard', 'DashboardController@index');

// Zarządzanie przedmiotami
$router->get('/items', 'ItemController@index');
$router->get('/items/create', 'ItemController@create');
$router->post('/items', 'ItemController@store');
$router->get('/items/{id}', 'ItemController@show');
$router->get('/items/{id}/edit', 'ItemController@edit');
$router->post('/items/{id}', 'ItemController@update');
$router->post('/items/{id}/delete', 'ItemController@delete');

// Upload zdjęć
$router->post('/items/{id}/images', 'ItemController@uploadImages');
$router->post('/items/{id}/images/{imageId}/delete', 'ItemController@deleteImage');
$router->post('/items/{id}/images/{imageId}/primary', 'ItemController@setPrimaryImage');

// Kategorie
$router->get('/categories', 'CategoryController@index');
$router->post('/categories', 'CategoryController@store');
$router->post('/categories/{id}', 'CategoryController@update');
$router->post('/categories/{id}/delete', 'CategoryController@delete');

// Rezerwacje
$router->get('/reservations', 'ReservationController@index');
$router->post('/reservations', 'ReservationController@store');
$router->post('/reservations/{id}/approve', 'ReservationController@approve');
$router->post('/reservations/{id}/reject', 'ReservationController@reject');
$router->post('/reservations/{id}/cancel', 'ReservationController@cancel');

// Wypożyczenia
$router->get('/loans', 'LoanController@index');
$router->get('/loans/create', 'LoanController@create');
$router->post('/loans', 'LoanController@store');
$router->get('/loans/{id}', 'LoanController@show');
$router->post('/loans/{id}/return', 'LoanController@return');
$router->post('/loans/{id}/extend', 'LoanController@extend');

// Transfery
$router->get('/transfers', 'TransferController@index');
$router->post('/transfers/request', 'TransferController@requestTransfer');
$router->post('/transfers/{id}/approve', 'TransferController@approve');
$router->post('/transfers/{id}/reject', 'TransferController@reject');
$router->post('/transfers/{id}/complete', 'TransferController@complete');

// Użytkownicy (tylko admin)
$router->get('/users', 'UserController@index');
$router->get('/users/create', 'UserController@create');
$router->post('/users', 'UserController@store');
$router->get('/users/{id}', 'UserController@show');
$router->get('/users/{id}/edit', 'UserController@edit');
$router->post('/users/{id}', 'UserController@update');

// Profil użytkownika
$router->get('/profile', 'UserController@profile');
$router->post('/profile', 'UserController@updateProfile');
$router->post('/profile/password', 'UserController@changePassword');

// Raporty
$router->get('/reports', 'ReportController@index');
$router->get('/reports/inventory', 'ReportController@inventory');
$router->get('/reports/loans', 'ReportController@loans');
$router->get('/reports/users', 'ReportController@users');
$router->get('/reports/items/{id}/history', 'ReportController@itemHistory');
$router->get('/reports/export/{type}', 'ReportController@export');

// API
$router->get('/api/items/search', 'Api\\ItemController@search');
$router->get('/api/items/available', 'Api\\ItemController@available');
$router->get('/api/items/{id}', 'Api\\ItemController@show');
$router->get('/api/categories', 'Api\\ItemController@categories');
$router->get('/api/users/search', 'Api\\UserController@search');
$router->get('/api/users/geodeci', 'Api\\UserController@geodeci');

// Obsługa routingu
try {
    $router->dispatch();
} catch (Exception $e) {
    if (CONFIG['app']['debug']) {
        echo '<pre>Error: ' . $e->getMessage() . '</pre>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    } else {
        logError('Router error: ' . $e->getMessage());
        include TEMPLATES_PATH . '/errors/404.php';
    }
}
