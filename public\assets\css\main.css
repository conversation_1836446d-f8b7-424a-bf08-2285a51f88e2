/* Główne style aplikacji */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Globalne style */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Navbar */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Statystyki dashboard */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--primary-color);
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Badges */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Timeline */
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 1.5rem;
    bottom: -1rem;
    width: 1px;
    background-color: #dee2e6;
}

/* Image gallery */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.image-item {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.image-item:hover {
    transform: scale(1.05);
    box-shadow: var(--box-shadow-lg);
}

.image-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.image-item .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-item:hover .overlay {
    opacity: 1;
}

/* Pagination */
.pagination .page-link {
    border-radius: var(--border-radius);
    margin: 0 0.125rem;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Search and filters */
.search-filters {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

/* Status badges */
.status-available {
    background-color: var(--success-color);
}

.status-reserved {
    background-color: var(--warning-color);
}

.status-lent {
    background-color: var(--info-color);
}

.status-maintenance {
    background-color: var(--danger-color);
}

.status-pending {
    background-color: var(--warning-color);
}

.status-approved {
    background-color: var(--success-color);
}

.status-rejected {
    background-color: var(--danger-color);
}

.status-cancelled {
    background-color: #6c757d;
}

.status-active {
    background-color: var(--success-color);
}

.status-returned {
    background-color: #6c757d;
}

.status-overdue {
    background-color: var(--danger-color);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
    
    .image-gallery {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Print styles */
@media print {
    .navbar,
    .btn,
    .pagination,
    .alert-dismissible .btn-close {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #343a40;
        --dark-color: #f8f9fa;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility classes */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-gray-800 {
    color: #5a5c69;
}

.shadow {
    box-shadow: var(--box-shadow-lg);
}

.border-0 {
    border: 0 !important;
}

.rounded-lg {
    border-radius: 0.75rem;
}
