/**
 * Główny plik JavaScript aplikacji
 */

// Globalne ustawienia
window.App = {
    config: window.APP_CONFIG || {},
    
    // Inicjalizacja aplikacji
    init() {
        this.setupCSRF();
        this.setupAlerts();
        this.setupTooltips();
        this.setupConfirmDialogs();
        this.setupAjaxForms();
        this.setupImageGallery();
        this.setupSearch();
        console.log('App initialized');
    },

    // Konfiguracja CSRF dla AJAX
    setupCSRF() {
        const token = this.config.csrfToken;
        if (token) {
            // Dodaj CSRF token do wszystkich AJAX requestów
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                        xhr.setRequestHeader("X-CSRFToken", token);
                    }
                }
            });
        }
    },

    // Automatyczne ukrywanie alertów
    setupAlerts() {
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    },

    // Inicjalizacja tooltipów
    setupTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // Potwierdzenia usuwania
    setupConfirmDialogs() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-confirm]') || e.target.closest('[data-confirm]')) {
                const element = e.target.matches('[data-confirm]') ? e.target : e.target.closest('[data-confirm]');
                const message = element.dataset.confirm || 'Czy na pewno chcesz wykonać tę akcję?';
                
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    },

    // Obsługa formularzy AJAX
    setupAjaxForms() {
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.ajax-form')) {
                e.preventDefault();
                this.submitAjaxForm(e.target);
            }
        });
    },

    // Wysyłanie formularza AJAX
    submitAjaxForm(form) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Pokazanie loadingu
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Wysyłanie...';
        
        fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('success', data.message || 'Operacja zakończona pomyślnie');
                
                // Przekierowanie jeśli podano
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
                
                // Reset formularza
                form.reset();
            } else {
                this.showAlert('danger', data.error || 'Wystąpił błąd');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('danger', 'Wystąpił błąd połączenia');
        })
        .finally(() => {
            // Przywrócenie przycisku
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    },

    // Wyświetlanie alertów
    showAlert(type, message) {
        const alertContainer = document.getElementById('alert-container') || document.querySelector('main');
        const alertId = 'alert-' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('afterbegin', alertHTML);
        
        // Automatyczne usunięcie po 5 sekundach
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    },

    // Galeria zdjęć
    setupImageGallery() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.gallery-image') || e.target.closest('.gallery-image')) {
                e.preventDefault();
                const img = e.target.matches('img') ? e.target : e.target.querySelector('img');
                if (img) {
                    this.showImageModal(img.src, img.alt);
                }
            }
        });
    },

    // Modal zdjęcia
    showImageModal(src, alt) {
        const modalHTML = `
            <div class="modal fade" id="imageModal" tabindex="-1">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${alt || 'Zdjęcie'}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="${src}" class="img-fluid" alt="${alt || 'Zdjęcie'}">
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Usuń poprzedni modal jeśli istnieje
        const existingModal = document.getElementById('imageModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
        
        // Usuń modal po zamknięciu
        document.getElementById('imageModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    // Wyszukiwanie na żywo
    setupSearch() {
        const searchInputs = document.querySelectorAll('.live-search');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.performLiveSearch(e.target);
                }, 300);
            });
        });
    },

    // Wykonanie wyszukiwania na żywo
    performLiveSearch(input) {
        const query = input.value.trim();
        const targetSelector = input.dataset.target;
        const url = input.dataset.url;
        
        if (!targetSelector || !url) return;
        
        const target = document.querySelector(targetSelector);
        if (!target) return;
        
        if (query.length < 2) {
            target.innerHTML = '';
            return;
        }
        
        // Pokazanie loadingu
        target.innerHTML = '<div class="text-center py-3"><div class="spinner-border"></div></div>';
        
        fetch(`${url}?q=${encodeURIComponent(query)}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                target.innerHTML = data.html || '';
            } else {
                target.innerHTML = '<div class="text-muted text-center py-3">Brak wyników</div>';
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            target.innerHTML = '<div class="text-danger text-center py-3">Błąd wyszukiwania</div>';
        });
    },

    // Upload plików
    setupFileUpload() {
        const fileInputs = document.querySelectorAll('.file-upload');
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.handleFileUpload(e.target);
            });
        });
    },

    // Obsługa uploadu plików
    handleFileUpload(input) {
        const files = input.files;
        const preview = document.querySelector(input.dataset.preview);
        
        if (!preview) return;
        
        preview.innerHTML = '';
        
        Array.from(files).forEach(file => {
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'img-thumbnail me-2 mb-2';
                    img.style.width = '100px';
                    img.style.height = '100px';
                    img.style.objectFit = 'cover';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(file);
            }
        });
    },

    // Formatowanie dat
    formatDate(date, format = 'dd.mm.yyyy') {
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        
        return format
            .replace('dd', day)
            .replace('mm', month)
            .replace('yyyy', year);
    },

    // Formatowanie liczb
    formatNumber(number, decimals = 0) {
        return new Intl.NumberFormat('pl-PL', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    },

    // Debounce funkcja
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Sprawdzenie uprawnień
    hasPermission(permission) {
        return this.config.permissions && this.config.permissions.includes(permission);
    },

    // Pobranie danych użytkownika
    getCurrentUser() {
        return this.config.user || null;
    }
};

// Inicjalizacja po załadowaniu DOM
document.addEventListener('DOMContentLoaded', () => {
    window.App.init();
});

// Eksport dla modułów
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.App;
}
