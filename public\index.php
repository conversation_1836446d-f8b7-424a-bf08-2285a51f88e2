<?php

/**
 * <PERSON>t wej<PERSON>cia aplikacji - System Magazynowy
 */

// Bootstrap aplikacji
$router = require_once __DIR__ . '/../config/bootstrap.php';

// Definicja tras

// Strona główna
$router->get('/', function() {
    if (isLoggedIn()) {
        redirect('/dashboard');
    } else {
        redirect('/login');
    }
});

// Uwierzytelnianie
$router->get('/login', 'AuthController@showLogin');
$router->post('/login', 'AuthController@login');
$router->get('/logout', 'AuthController@logout');

// Dashboard
$router->get('/dashboard', 'DashboardController@index');

// Zarządzanie przedmiotami
$router->get('/items', 'ItemController@index');
$router->get('/items/create', 'ItemController@create');
$router->post('/items', 'ItemController@store');
$router->get('/items/{id}', 'ItemController@show');
$router->get('/items/{id}/edit', 'ItemController@edit');
$router->post('/items/{id}', 'ItemController@update');
$router->post('/items/{id}/delete', 'ItemController@delete');
$router->post('/items/{id}/images', 'ItemController@uploadImages');
$router->post('/items/images/{imageId}/delete', 'ItemController@deleteImage');

// Kategorie
$router->get('/categories', 'CategoryController@index');
$router->post('/categories', 'CategoryController@store');
$router->post('/categories/{id}', 'CategoryController@update');
$router->post('/categories/{id}/delete', 'CategoryController@delete');

// Rezerwacje
$router->get('/reservations', 'ReservationController@index');
$router->post('/reservations', 'ReservationController@store');
$router->post('/reservations/{id}/approve', 'ReservationController@approve');
$router->post('/reservations/{id}/reject', 'ReservationController@reject');
$router->post('/reservations/{id}/cancel', 'ReservationController@cancel');

// Wypożyczenia
$router->get('/loans', 'LoanController@index');
$router->get('/loans/create', 'LoanController@create');
$router->post('/loans', 'LoanController@store');
$router->get('/loans/{id}', 'LoanController@show');
$router->post('/loans/{id}/return', 'LoanController@return');
$router->post('/loans/{id}/extend', 'LoanController@extend');

// Transfery
$router->get('/transfers', 'TransferController@index');
$router->post('/transfers/request', 'TransferController@requestTransfer');
$router->post('/transfers/{id}/approve', 'TransferController@approve');
$router->post('/transfers/{id}/reject', 'TransferController@reject');
$router->post('/transfers/{id}/complete', 'TransferController@complete');

// Raporty
$router->get('/reports', 'ReportController@index');
$router->get('/reports/inventory', 'ReportController@inventory');
$router->get('/reports/loans', 'ReportController@loans');
$router->get('/reports/users', 'ReportController@users');
$router->get('/reports/history/{itemId}', 'ReportController@itemHistory');
$router->get('/reports/export/{type}', 'ReportController@export');

// Użytkownicy (tylko admin)
$router->get('/users', 'UserController@index');
$router->get('/users/create', 'UserController@create');
$router->post('/users', 'UserController@store');
$router->get('/users/{id}', 'UserController@show');
$router->get('/users/{id}/edit', 'UserController@edit');
$router->post('/users/{id}', 'UserController@update');

// Profil użytkownika
$router->get('/profile', 'UserController@profile');
$router->post('/profile', 'UserController@updateProfile');
$router->post('/profile/password', 'UserController@changePassword');
$router->post('/users/{id}/delete', 'UserController@delete');

// Profil użytkownika
$router->get('/profile', 'UserController@profile');
$router->post('/profile', 'UserController@updateProfile');
$router->post('/profile/password', 'UserController@changePassword');

// API endpoints
$router->get('/api/items/search', 'Api\ItemController@search');
$router->get('/api/users/search', 'Api\UserController@search');
$router->get('/api/dashboard/stats', 'Api\DashboardController@stats');

// Obsługa plików statycznych w trybie deweloperskim
if (CONFIG['app']['env'] === 'development') {
    $router->get('/uploads/{path}', function($path) {
        $filePath = UPLOAD_PATH . '/' . $path;
        
        if (!file_exists($filePath) || !is_file($filePath)) {
            http_response_code(404);
            exit;
        }
        
        $mimeType = mime_content_type($filePath);
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($filePath));
        readfile($filePath);
        exit;
    });
}

// Obsługa błędów 404
$router->setNotFoundHandler(function() {
    http_response_code(404);
    
    if (isAjax()) {
        jsonResponse(['error' => 'Nie znaleziono zasobu'], 404);
    }
    
    include TEMPLATES_PATH . '/errors/404.php';
});

// Obsługa błędów 500
$router->setErrorHandler(function($exception) {
    http_response_code(500);
    
    // Logowanie błędu
    logError($exception->getMessage(), [
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    if (isAjax()) {
        $message = CONFIG['app']['debug'] ? $exception->getMessage() : 'Wystąpił błąd serwera';
        jsonResponse(['error' => $message], 500);
    }
    
    if (CONFIG['app']['debug']) {
        echo '<h1>Błąd aplikacji</h1>';
        echo '<p><strong>Wiadomość:</strong> ' . e($exception->getMessage()) . '</p>';
        echo '<p><strong>Plik:</strong> ' . e($exception->getFile()) . '</p>';
        echo '<p><strong>Linia:</strong> ' . $exception->getLine() . '</p>';
        echo '<pre>' . e($exception->getTraceAsString()) . '</pre>';
    } else {
        include TEMPLATES_PATH . '/errors/500.php';
    }
});

// Uruchomienie routera
try {
    $router->run();
} catch (Exception $e) {
    // Obsługa krytycznych błędów
    if (CONFIG['app']['debug']) {
        throw $e;
    } else {
        logError('Krytyczny błąd aplikacji: ' . $e->getMessage());
        http_response_code(500);
        echo 'Wystąpił błąd serwera. Skontaktuj się z administratorem.';
    }
}
