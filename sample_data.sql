-- Przykładowe dane do testowania systemu magazynowego
-- Wykonaj po zaimportowaniu database_schema.sql

-- Kategorie
INSERT INTO categories (name, description, is_lendable) VALUES
('Sprzęt geodezyjny', 'Instrumenty i urządzenia geodezyjne', 1),
('Sprzęt komputerowy', 'Laptopy, tablety, drukarki', 1),
('<PERSON>prz<PERSON>t biurowy', 'Meble i akcesoria biurowe', 1),
('Narzędzia', 'Narzędzia pomocnicze', 1);

-- Użytkownicy (hasło dla wszystkich: password123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_active) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<PERSON>u', 'admin', 1),
('j<PERSON><PERSON><PERSON>', 'j.k<PERSON><PERSON><PERSON>@firma.pl', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jan', 'Kowalski', 'geodeta', 1),
('anowak', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Anna', 'Nowak', 'geodeta', 1),
('pwisniak', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Piotr', 'Wiśniak', 'geodeta', 1),
('magazynier', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Magazynier', 'Główny', 'admin', 1);

-- Przedmioty
INSERT INTO items (name, description, details, category_id, status, is_lendable) VALUES
-- Sprzęt geodezyjny
('Tachimetr Leica TS06', 'Tachimetr elektroniczny Leica TS06 plus z laserem', 'Dokładność kątowa: 2", Zasięg: 500m, Kompensator dwuosiowy', 1, 'available', 1),
('Niwelator Leica NA724', 'Niwelator automatyczny Leica NA724', 'Dokładność: 2.5mm/km, Powiększenie: 24x, Kompensator magnetyczny', 1, 'available', 1),
('GPS RTK Trimble R8s', 'Odbiornik GPS RTK Trimble R8s', 'Dokładność: 1cm + 1ppm, Czas inicjalizacji: <10s, Bluetooth i WiFi', 1, 'lent', 1),
('Statyw drewniany', 'Statyw drewniany do instrumentów geodezyjnych', 'Wysokość: 1.05-1.70m, Waga: 2.8kg, Głowica z gwintem 5/8"', 1, 'available', 1),
('Łata niwelacyjna 4m', 'Łata niwelacyjna teleskopowa 4m', 'Podziałka: mm, Materiał: aluminium, Waga: 1.2kg', 1, 'available', 1),

-- Sprzęt komputerowy
('Laptop Dell Latitude 5520', 'Laptop Dell Latitude 5520 i5-1135G7', 'Intel i5-1135G7, 16GB RAM, 512GB SSD, Windows 11 Pro', 2, 'available', 1),
('Tablet Samsung Galaxy Tab S7', 'Tablet Samsung Galaxy Tab S7 11"', 'Snapdragon 865+, 6GB RAM, 128GB, S Pen w zestawie', 2, 'lent', 1),
('Drukarka HP LaserJet Pro', 'Drukarka laserowa HP LaserJet Pro M404dn', 'Druk A4, 38 str/min, duplex, sieć Ethernet', 2, 'available', 1),
('Projektor Epson EB-X41', 'Projektor multimedialny Epson EB-X41', '3600 lumenów, XGA 1024x768, HDMI, VGA, USB', 2, 'available', 1),

-- Sprzęt biurowy
('Krzesło biurowe ergonomiczne', 'Krzesło biurowe z regulacją wysokości', 'Regulacja wysokości, podłokietniki, oparcie lędźwiowe', 3, 'available', 1),
('Biurko regulowane elektrycznie', 'Biurko z regulacją wysokości 120x80cm', 'Wysokość: 65-130cm, Blat: biały, Stelaż: czarny', 3, 'available', 1),
('Monitor Dell 24" U2419H', 'Monitor Dell UltraSharp 24" Full HD', '1920x1080, IPS, USB-C, HDMI, DisplayPort', 2, 'available', 1),

-- Narzędzia
('Młotek geologiczny', 'Młotek geologiczny z kilofem', 'Waga: 600g, Trzonek: drewniany, Stal hartowana', 4, 'available', 1),
('Taśma miernicza 50m', 'Taśma miernicza stalowa 50m', 'Dokładność: klasa II, Obudowa: ABS, Korbka składana', 4, 'available', 1),
('Spray do oznakowania', 'Spray do oznakowania terenu - czerwony', 'Kolor: czerwony, Pojemność: 500ml, Odporny na warunki atmosferyczne', 4, 'available', 1);

-- Przykładowe wypożyczenia
INSERT INTO loans (item_id, borrower_id, lender_id, loan_date, expected_return_date, status, notes) VALUES
(3, 2, 1, '2024-01-15', '2024-01-29', 'active', 'Pomiary na budowie osiedla Słoneczne'),
(7, 3, 1, '2024-01-10', '2024-01-24', 'active', 'Prezentacja dla klienta');

-- Aktualizacja statusu przedmiotów wypożyczonych
UPDATE items SET status = 'lent', current_holder_id = 2 WHERE id = 3;
UPDATE items SET status = 'lent', current_holder_id = 3 WHERE id = 7;

-- Przykładowe rezerwacje
INSERT INTO reservations (item_id, user_id, status, requested_date, notes) VALUES
(1, 4, 'pending', '2024-01-25', 'Potrzebuję na pomiary kontrolne'),
(6, 2, 'approved', '2024-01-30', 'Laptop na wyjazd służbowy'),
(9, 3, 'pending', '2024-02-01', 'Prezentacja w biurze klienta');

-- Przykładowe transfery
INSERT INTO transfers (item_id, from_user_id, to_user_id, transfer_type, processed_by_id, transfer_date, notes) VALUES
(3, NULL, 2, 'loan', 1, '2024-01-15', 'Wypożyczenie GPS RTK'),
(7, NULL, 3, 'loan', 1, '2024-01-10', 'Wypożyczenie tabletu');

-- Przykładowe prośby o transfer
INSERT INTO transfer_requests (item_id, requester_id, current_holder_id, status, message) VALUES
(3, 4, 2, 'pending', 'Potrzebuję GPS na pilny pomiar. Czy możesz przekazać po zakończeniu swojego projektu?');

-- Informacja o hasłach
-- UWAGA: Wszystkie konta mają hasło: password123
-- Pamiętaj o zmianie haseł po pierwszym logowaniu!

-- Konta testowe:
-- admin / admin123 - Administrator systemu
-- jkowalski / password123 - Geodeta Jan Kowalski  
-- anowak / password123 - Geodeta Anna Nowak
-- pwisniak / password123 - Geodeta Piotr Wiśniak
-- magazynier / password123 - Magazynier
