<?php

namespace App\Controllers\Api;

use App\Models\Item;
use App\Models\Category;
use Exception;

/**
 * API kontroler przedmiotów
 */
class ItemController
{
    private Item $itemModel;
    private Category $categoryModel;

    public function __construct()
    {
        $this->itemModel = new Item();
        $this->categoryModel = new Category();
    }

    /**
     * Wyszukiwanie przedmiotów
     */
    public function search(): void
    {
        try {
            $query = trim($_GET['q'] ?? '');
            $categoryId = !empty($_GET['category']) ? (int)$_GET['category'] : null;
            $status = !empty($_GET['status']) ? $_GET['status'] : null;
            $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));

            if (strlen($query) < 2) {
                jsonResponse([
                    'success' => true,
                    'items' => [],
                    'message' => 'Wprowadź co najmniej 2 znaki'
                ]);
                return;
            }

            $filters = [
                'search' => $query,
                'category_id' => $categoryId,
                'status' => $status
            ];

            $items = $this->itemModel->getAll($limit, 0, $filters);

            // Formatowanie wyników
            $formattedItems = array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'description' => $item['description'],
                    'category_name' => $item['category_name'],
                    'status' => $item['status'],
                    'is_lendable' => $item['is_lendable'],
                    'current_holder_name' => $item['current_holder_name'],
                    'primary_image' => $item['primary_image'] ? uploadUrl('items/' . $item['primary_image']) : null,
                    'url' => url("/items/{$item['id']}")
                ];
            }, $items);

            jsonResponse([
                'success' => true,
                'items' => $formattedItems,
                'count' => count($formattedItems)
            ]);

        } catch (Exception $e) {
            logError('Błąd wyszukiwania przedmiotów: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd podczas wyszukiwania'], 500);
        }
    }

    /**
     * Dostępne przedmioty
     */
    public function available(): void
    {
        try {
            $categoryId = !empty($_GET['category']) ? (int)$_GET['category'] : null;
            $items = $this->itemModel->getAvailable($categoryId);

            $formattedItems = array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'category_name' => $item['category_name'],
                    'primary_image' => $item['primary_image'] ? uploadUrl('items/' . $item['primary_image']) : null
                ];
            }, $items);

            jsonResponse([
                'success' => true,
                'items' => $formattedItems
            ]);

        } catch (Exception $e) {
            logError('Błąd pobierania dostępnych przedmiotów: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd'], 500);
        }
    }

    /**
     * Szczegóły przedmiotu
     */
    public function show(int $id): void
    {
        try {
            $item = $this->itemModel->getById($id);
            
            if (!$item) {
                jsonResponse(['error' => 'Przedmiot nie został znaleziony'], 404);
                return;
            }

            $formattedItem = [
                'id' => $item['id'],
                'name' => $item['name'],
                'description' => $item['description'],
                'details' => $item['details'],
                'category_name' => $item['category_name'],
                'status' => $item['status'],
                'is_lendable' => $item['is_lendable'],
                'current_holder_name' => $item['current_holder_name'],
                'current_holder_username' => $item['current_holder_username'],
                'created_at' => $item['created_at']
            ];

            jsonResponse([
                'success' => true,
                'item' => $formattedItem
            ]);

        } catch (Exception $e) {
            logError('Błąd pobierania przedmiotu: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd'], 500);
        }
    }

    /**
     * Kategorie
     */
    public function categories(): void
    {
        try {
            $categories = $this->categoryModel->getAll();

            $formattedCategories = array_map(function($category) {
                return [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'description' => $category['description'],
                    'is_lendable' => $category['is_lendable'],
                    'items_count' => $category['items_count']
                ];
            }, $categories);

            jsonResponse([
                'success' => true,
                'categories' => $formattedCategories
            ]);

        } catch (Exception $e) {
            logError('Błąd pobierania kategorii: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd'], 500);
        }
    }
}
