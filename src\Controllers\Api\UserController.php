<?php

namespace App\Controllers\Api;

use App\Models\User;
use Exception;

/**
 * API kontroler użytkowników
 */
class UserController
{
    private User $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Wyszukiwanie użytkowników
     */
    public function search(): void
    {
        try {
            $query = trim($_GET['q'] ?? '');
            $role = !empty($_GET['role']) ? $_GET['role'] : null;
            $limit = min(20, max(1, (int)($_GET['limit'] ?? 10)));

            if (strlen($query) < 2) {
                jsonResponse([
                    'success' => true,
                    'users' => [],
                    'message' => 'Wprowadź co najmniej 2 znaki'
                ]);
                return;
            }

            $users = $this->userModel->search($query, $role, $limit);

            $formattedUsers = array_map(function($user) {
                return [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'full_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'is_active' => $user['is_active']
                ];
            }, $users);

            jsonResponse([
                'success' => true,
                'users' => $formattedUsers,
                'count' => count($formattedUsers)
            ]);

        } catch (Exception $e) {
            logError('Błąd wyszukiwania użytkowników: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd podczas wyszukiwania'], 500);
        }
    }

    /**
     * Lista geodetów
     */
    public function geodeci(): void
    {
        try {
            $users = $this->userModel->getByRole('geodeta');

            $formattedUsers = array_map(function($user) {
                return [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'full_name' => $user['first_name'] . ' ' . $user['last_name'],
                    'email' => $user['email']
                ];
            }, $users);

            jsonResponse([
                'success' => true,
                'users' => $formattedUsers
            ]);

        } catch (Exception $e) {
            logError('Błąd pobierania geodetów: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd'], 500);
        }
    }
}
