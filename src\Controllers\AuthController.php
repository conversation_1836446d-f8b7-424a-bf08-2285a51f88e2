<?php

namespace App\Controllers;

use App\Models\User;
use Exception;

/**
 * Kontroler uwierzytelniania
 */
class AuthController
{
    private User $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Wyświetlenie formularza logowania
     */
    public function showLogin(): void
    {
        // Jeśli użytkownik jest już zalogowany, przekieruj na dashboard
        if ($this->isLoggedIn()) {
            redirect('/dashboard');
        }

        $pageTitle = 'Logowanie';
        include TEMPLATES_PATH . '/auth/login.php';
    }

    /**
     * Proces logowania
     */
    public function login(): void
    {
        try {
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            $rememberMe = isset($_POST['remember_me']);

            if (empty($username) || empty($password)) {
                throw new Exception('Nazwa użytkownika i hasło są wymagane');
            }

            // Sprawdzenie blokady konta
            $this->checkLoginAttempts($username);

            // Pobranie użytkownika
            $user = $this->userModel->getByUsername($username);
            
            if (!$user) {
                $this->recordFailedLogin($username);
                throw new Exception('Nieprawidłowa nazwa użytkownika lub hasło');
            }

            // Sprawdzenie czy konto jest aktywne
            if (!$user['is_active']) {
                throw new Exception('Konto zostało dezaktywowane');
            }

            // Weryfikacja hasła
            if (!$this->userModel->verifyPassword($password, $user['password_hash'])) {
                $this->recordFailedLogin($username);
                throw new Exception('Nieprawidłowa nazwa użytkownika lub hasło');
            }

            // Zalogowanie użytkownika
            $this->loginUser($user, $rememberMe);

            // Wyczyszczenie nieudanych prób logowania
            $this->clearFailedLogins($username);

            // Przekierowanie
            $redirectUrl = $_SESSION['redirect_after_login'] ?? '/dashboard';
            unset($_SESSION['redirect_after_login']);
            
            if (isAjax()) {
                jsonResponse(['success' => true, 'redirect' => $redirectUrl]);
            } else {
                redirect($redirectUrl);
            }

        } catch (Exception $e) {
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/login');
            }
        }
    }

    /**
     * Wylogowanie użytkownika
     */
    public function logout(): void
    {
        // Usunięcie sesji z bazy danych
        if (isset($_SESSION['session_id'])) {
            $this->deleteSession($_SESSION['session_id']);
        }

        // Wyczyszczenie sesji
        session_unset();
        session_destroy();

        // Usunięcie cookie sesji
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }

        redirect('/login');
    }

    /**
     * Sprawdzenie czy użytkownik jest zalogowany
     */
    public function isLoggedIn(): bool
    {
        if (!isset($_SESSION['user']) || !isset($_SESSION['session_id'])) {
            return false;
        }

        // Sprawdzenie czy sesja istnieje w bazie danych
        if (!$this->validateSession($_SESSION['session_id'])) {
            $this->logout();
            return false;
        }

        return true;
    }

    /**
     * Pobranie aktualnego użytkownika
     */
    public function getCurrentUser(): ?array
    {
        return $_SESSION['user'] ?? null;
    }

    /**
     * Sprawdzenie uprawnień
     */
    public function hasPermission(string $permission): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }

        $user = $this->getCurrentUser();
        $userRole = $user['role'];
        $rolePermissions = CONFIG['roles'][$userRole]['permissions'] ?? [];

        return in_array($permission, $rolePermissions);
    }

    /**
     * Sprawdzenie roli
     */
    public function hasRole(string $role): bool
    {
        if (!$this->isLoggedIn()) {
            return false;
        }

        $user = $this->getCurrentUser();
        return $user['role'] === $role;
    }

    /**
     * Zalogowanie użytkownika
     */
    private function loginUser(array $user, bool $rememberMe = false): void
    {
        // Regeneracja ID sesji dla bezpieczeństwa
        session_regenerate_id(true);

        // Utworzenie rekordu sesji w bazie danych
        $sessionId = $this->createSession($user['id'], $rememberMe);

        // Zapisanie danych w sesji
        $_SESSION['user'] = [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'role' => $user['role'],
            'full_name' => $user['first_name'] . ' ' . $user['last_name']
        ];
        $_SESSION['session_id'] = $sessionId;
        $_SESSION['login_time'] = time();
    }

    /**
     * Utworzenie sesji w bazie danych
     */
    private function createSession(int $userId, bool $rememberMe = false): string
    {
        $sessionId = session_id();
        $expiresAt = date('Y-m-d H:i:s', time() + CONFIG['session']['lifetime']);
        
        if ($rememberMe) {
            $expiresAt = date('Y-m-d H:i:s', time() + (30 * 24 * 3600)); // 30 dni
        }

        $query = "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at) 
                  VALUES (:id, :user_id, :ip_address, :user_agent, :expires_at)";

        $params = [
            'id' => $sessionId,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'expires_at' => $expiresAt
        ];

        DB->insert($query, $params);

        return $sessionId;
    }

    /**
     * Walidacja sesji
     */
    private function validateSession(string $sessionId): bool
    {
        $query = "SELECT id FROM user_sessions 
                  WHERE id = :id AND expires_at > NOW()";

        $session = DB->selectOne($query, ['id' => $sessionId]);

        return !empty($session);
    }

    /**
     * Usunięcie sesji
     */
    private function deleteSession(string $sessionId): void
    {
        $query = "DELETE FROM user_sessions WHERE id = :id";
        DB->delete($query, ['id' => $sessionId]);
    }

    /**
     * Sprawdzenie nieudanych prób logowania
     */
    private function checkLoginAttempts(string $username): void
    {
        $cacheKey = 'login_attempts_' . md5($username);
        
        if (!isset($_SESSION[$cacheKey])) {
            return;
        }

        $attempts = $_SESSION[$cacheKey];
        
        if ($attempts['count'] >= CONFIG['security']['max_login_attempts']) {
            $lockoutEnd = $attempts['locked_until'] ?? 0;
            
            if (time() < $lockoutEnd) {
                $remainingTime = ceil(($lockoutEnd - time()) / 60);
                throw new Exception("Konto zostało zablokowane na {$remainingTime} minut");
            } else {
                // Blokada wygasła
                unset($_SESSION[$cacheKey]);
            }
        }
    }

    /**
     * Zapisanie nieudanej próby logowania
     */
    private function recordFailedLogin(string $username): void
    {
        $cacheKey = 'login_attempts_' . md5($username);
        
        if (!isset($_SESSION[$cacheKey])) {
            $_SESSION[$cacheKey] = ['count' => 0, 'locked_until' => 0];
        }

        $_SESSION[$cacheKey]['count']++;

        if ($_SESSION[$cacheKey]['count'] >= CONFIG['security']['max_login_attempts']) {
            $_SESSION[$cacheKey]['locked_until'] = time() + CONFIG['security']['lockout_duration'];
        }
    }

    /**
     * Wyczyszczenie nieudanych prób logowania
     */
    private function clearFailedLogins(string $username): void
    {
        $cacheKey = 'login_attempts_' . md5($username);
        unset($_SESSION[$cacheKey]);
    }

    /**
     * Czyszczenie wygasłych sesji
     */
    public function cleanupExpiredSessions(): void
    {
        $query = "DELETE FROM user_sessions WHERE expires_at < NOW()";
        DB->delete($query);
    }
}
