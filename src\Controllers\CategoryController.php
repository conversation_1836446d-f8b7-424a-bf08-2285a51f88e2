<?php

namespace App\Controllers;

use App\Models\Category;
use Exception;

/**
 * Kontroler kategorii
 */
class CategoryController
{
    private Category $categoryModel;

    public function __construct()
    {
        $this->categoryModel = new Category();
    }

    /**
     * Lista kategorii
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                flash('error', 'Brak uprawnień');
                redirect('/dashboard');
            }

            $categories = $this->categoryModel->getAll();
            $stats = $this->categoryModel->getStats();

            $pageTitle = 'Kategorie';
            include TEMPLATES_PATH . '/categories/index.php';

        } catch (Exception $e) {
            logError('Błąd listy kategorii: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania kategorii');
            redirect('/dashboard');
        }
    }

    /**
     * Utworzenie kategorii
     */
    public function store(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/categories');
                }
            }

            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'is_lendable' => isset($_POST['is_lendable'])
            ];

            $categoryId = $this->categoryModel->create($data);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Kategoria została dodana pomyślnie',
                    'category_id' => $categoryId
                ]);
            } else {
                flash('success', 'Kategoria została dodana pomyślnie');
                redirect('/categories');
            }

        } catch (Exception $e) {
            logError('Błąd dodawania kategorii: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/categories');
            }
        }
    }

    /**
     * Aktualizacja kategorii
     */
    public function update(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/categories');
                }
            }

            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'is_lendable' => isset($_POST['is_lendable'])
            ];

            $this->categoryModel->update($id, $data);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Kategoria została zaktualizowana pomyślnie'
                ]);
            } else {
                flash('success', 'Kategoria została zaktualizowana pomyślnie');
                redirect('/categories');
            }

        } catch (Exception $e) {
            logError('Błąd aktualizacji kategorii: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/categories');
            }
        }
    }

    /**
     * Usunięcie kategorii
     */
    public function delete(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/categories');
                }
            }

            $this->categoryModel->delete($id);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Kategoria została usunięta pomyślnie'
                ]);
            } else {
                flash('success', 'Kategoria została usunięta pomyślnie');
                redirect('/categories');
            }

        } catch (Exception $e) {
            logError('Błąd usuwania kategorii: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/categories');
            }
        }
    }
}
