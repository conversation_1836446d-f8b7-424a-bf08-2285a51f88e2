<?php

namespace App\Controllers;

use App\Models\User;
use Exception;

/**
 * Kontroler dashboardu
 */
class DashboardController
{
    private User $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Strona główna dashboardu
     */
    public function index(): void
    {
        try {
            // Middleware już sprawdził czy użytkownik jest zalogowany
            $authController = new AuthController();
            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            // Pobranie statystyk
            $stats = $this->getDashboardStats($currentUser['id'], $isAdmin);

            // Pobranie ostatnich aktywności
            $recentActivities = $this->getRecentActivities($currentUser['id'], $isAdmin);

            // Pobranie alertów i powiadomień
            $alerts = $this->getAlerts($currentUser['id'], $isAdmin);

            $pageTitle = 'Dashboard';
            include TEMPLATES_PATH . '/dashboard/index.php';

        } catch (Exception $e) {
            logError('Błąd dashboardu: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania dashboardu');
            redirect('/login');
        }
    }

    /**
     * Dashboard dla geodety
     */
    public function geodeta(): void
    {
        try {
            // DEBUG: Sprawdź sesję
            error_log("DEBUG geodeta(): Sesja: " . print_r($_SESSION, true));

            $authController = new AuthController();
            $currentUser = $authController->getCurrentUser();

            // DEBUG: Sprawdź użytkownika
            error_log("DEBUG geodeta(): Current user: " . print_r($currentUser, true));

            // Sprawdź czy użytkownik istnieje
            if (!$currentUser) {
                error_log("DEBUG geodeta(): Brak użytkownika, przekierowanie na login");
                redirect('/login');
                return;
            }

            // Sprawdź czy to rzeczywiście geodeta
            if ($currentUser['role'] !== 'geodeta') {
                error_log("DEBUG geodeta(): Nieprawidłowa rola: " . $currentUser['role']);
                redirect('/dashboard');
                return;
            }

            // TYMCZASOWO: Proste dane dla geodety
            $stats = [
                'my_items' => 0,
                'my_reservations' => 0,
                'available_items' => 0,
                'pending_requests' => 0
            ];
            $recentActivities = [];
            $alerts = [];

            $pageTitle = 'Panel Geodety';
            $isAdmin = false;

            error_log("DEBUG geodeta(): Ładowanie template dla geodety");
            include TEMPLATES_PATH . '/dashboard/geodeta.php';

        } catch (Exception $e) {
            logError('Błąd dashboardu geodety: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania panelu');
            redirect('/login');
        }
    }

    /**
     * Pobranie statystyk dla dashboardu
     */
    private function getDashboardStats(int $userId, bool $isAdmin): array
    {
        $stats = [];

        if ($isAdmin) {
            // Statystyki dla administratora
            $stats['total_items'] = DB->count('items');
            $stats['available_items'] = DB->count('items', 'status = "available"');
            $stats['lent_items'] = DB->count('items', 'status = "lent"');
            $stats['reserved_items'] = DB->count('items', 'status = "reserved"');
            $stats['maintenance_items'] = DB->count('items', 'status = "maintenance"');
            
            $stats['total_users'] = DB->count('users', 'is_active = 1');
            $stats['active_loans'] = DB->count('loans', 'status = "active"');
            $stats['pending_reservations'] = DB->count('reservations', 'status = "pending"');
            $stats['pending_transfers'] = DB->count('transfer_requests', 'status = "pending"');
            $stats['overdue_loans'] = DB->count('loans', 'status = "active" AND expected_return_date < CURDATE()');
            
        } else {
            // Statystyki dla geodety
            $stats['my_active_loans'] = DB->count('loans', 'borrower_id = :user_id AND status = "active"', ['user_id' => $userId]);
            $stats['my_pending_reservations'] = DB->count('reservations', 'user_id = :user_id AND status = "pending"', ['user_id' => $userId]);
            $stats['my_transfer_requests'] = DB->count('transfer_requests', 'requester_id = :user_id AND status = "pending"', ['user_id' => $userId]);
            $stats['available_items'] = DB->count('items', 'status = "available" AND is_lendable = 1');
        }

        return $stats;
    }

    /**
     * Pobranie ostatnich aktywności
     */
    private function getRecentActivities(int $userId, bool $isAdmin): array
    {
        if ($isAdmin) {
            // Ostatnie aktywności dla administratora
            $query = "
                SELECT 
                    'loan' as type,
                    l.id,
                    l.loan_date as date,
                    CONCAT(u.first_name, ' ', u.last_name) as user_name,
                    i.name as item_name,
                    l.status
                FROM loans l
                JOIN users u ON l.borrower_id = u.id
                JOIN items i ON l.item_id = i.id
                WHERE l.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                
                UNION ALL
                
                SELECT 
                    'reservation' as type,
                    r.id,
                    r.requested_date as date,
                    CONCAT(u.first_name, ' ', u.last_name) as user_name,
                    i.name as item_name,
                    r.status
                FROM reservations r
                JOIN users u ON r.user_id = u.id
                JOIN items i ON r.item_id = i.id
                WHERE r.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                
                ORDER BY date DESC
                LIMIT 10
            ";
        } else {
            // Ostatnie aktywności dla geodety
            $query = "
                SELECT 
                    'loan' as type,
                    l.id,
                    l.loan_date as date,
                    'Moje wypożyczenie' as user_name,
                    i.name as item_name,
                    l.status
                FROM loans l
                JOIN items i ON l.item_id = i.id
                WHERE l.borrower_id = :user_id AND l.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                
                UNION ALL
                
                SELECT 
                    'reservation' as type,
                    r.id,
                    r.requested_date as date,
                    'Moja rezerwacja' as user_name,
                    i.name as item_name,
                    r.status
                FROM reservations r
                JOIN items i ON r.item_id = i.id
                WHERE r.user_id = :user_id AND r.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                
                ORDER BY date DESC
                LIMIT 10
            ";
        }

        $params = $isAdmin ? [] : ['user_id' => $userId];
        return DB->select($query, $params);
    }

    /**
     * Pobranie alertów i powiadomień
     */
    private function getAlerts(int $userId, bool $isAdmin): array
    {
        $alerts = [];

        if ($isAdmin) {
            // Alerty dla administratora
            
            // Przeterminowane wypożyczenia
            $overdueLoans = DB->select("
                SELECT l.id, i.name as item_name, CONCAT(u.first_name, ' ', u.last_name) as borrower_name,
                       l.expected_return_date, DATEDIFF(CURDATE(), l.expected_return_date) as days_overdue
                FROM loans l
                JOIN items i ON l.item_id = i.id
                JOIN users u ON l.borrower_id = u.id
                WHERE l.status = 'active' AND l.expected_return_date < CURDATE()
                ORDER BY l.expected_return_date ASC
                LIMIT 5
            ");

            foreach ($overdueLoans as $loan) {
                $alerts[] = [
                    'type' => 'danger',
                    'icon' => 'exclamation-triangle',
                    'title' => 'Przeterminowane wypożyczenie',
                    'message' => "Przedmiot \"{$loan['item_name']}\" wypożyczony przez {$loan['borrower_name']} jest przeterminowany o {$loan['days_overdue']} dni",
                    'link' => "/loans/{$loan['id']}"
                ];
            }

            // Oczekujące rezerwacje
            $pendingReservations = DB->count('reservations', 'status = "pending"');
            if ($pendingReservations > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'icon' => 'clock',
                    'title' => 'Oczekujące rezerwacje',
                    'message' => "Masz {$pendingReservations} oczekujących rezerwacji do zatwierdzenia",
                    'link' => '/reservations'
                ];
            }

            // Oczekujące transfery
            $pendingTransfers = DB->count('transfer_requests', 'status = "pending"');
            if ($pendingTransfers > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'icon' => 'arrow-left-right',
                    'title' => 'Oczekujące transfery',
                    'message' => "Masz {$pendingTransfers} oczekujących próśb o transfer",
                    'link' => '/transfers'
                ];
            }

        } else {
            // Alerty dla geodety
            
            // Moje przeterminowane wypożyczenia
            $myOverdueLoans = DB->select("
                SELECT l.id, i.name as item_name, l.expected_return_date,
                       DATEDIFF(CURDATE(), l.expected_return_date) as days_overdue
                FROM loans l
                JOIN items i ON l.item_id = i.id
                WHERE l.borrower_id = :user_id AND l.status = 'active' AND l.expected_return_date < CURDATE()
                ORDER BY l.expected_return_date ASC
            ", ['user_id' => $userId]);

            foreach ($myOverdueLoans as $loan) {
                $alerts[] = [
                    'type' => 'danger',
                    'icon' => 'exclamation-triangle',
                    'title' => 'Przeterminowane wypożyczenie',
                    'message' => "Przedmiot \"{$loan['item_name']}\" jest przeterminowany o {$loan['days_overdue']} dni",
                    'link' => "/loans/{$loan['id']}"
                ];
            }

            // Zbliżające się terminy zwrotu
            $upcomingReturns = DB->select("
                SELECT l.id, i.name as item_name, l.expected_return_date,
                       DATEDIFF(l.expected_return_date, CURDATE()) as days_left
                FROM loans l
                JOIN items i ON l.item_id = i.id
                WHERE l.borrower_id = :user_id AND l.status = 'active' 
                AND l.expected_return_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 DAY)
                ORDER BY l.expected_return_date ASC
            ", ['user_id' => $userId]);

            foreach ($upcomingReturns as $loan) {
                $alerts[] = [
                    'type' => 'warning',
                    'icon' => 'clock',
                    'title' => 'Zbliżający się termin zwrotu',
                    'message' => "Przedmiot \"{$loan['item_name']}\" należy zwrócić za {$loan['days_left']} dni",
                    'link' => "/loans/{$loan['id']}"
                ];
            }

            // Zatwierdzone rezerwacje
            $approvedReservations = DB->select("
                SELECT r.id, i.name as item_name, r.requested_date
                FROM reservations r
                JOIN items i ON r.item_id = i.id
                WHERE r.user_id = :user_id AND r.status = 'approved'
                ORDER BY r.requested_date ASC
                LIMIT 3
            ", ['user_id' => $userId]);

            foreach ($approvedReservations as $reservation) {
                $alerts[] = [
                    'type' => 'success',
                    'icon' => 'check-circle',
                    'title' => 'Zatwierdzona rezerwacja',
                    'message' => "Twoja rezerwacja przedmiotu \"{$reservation['item_name']}\" została zatwierdzona",
                    'link' => "/reservations"
                ];
            }
        }

        return $alerts;
    }
}
