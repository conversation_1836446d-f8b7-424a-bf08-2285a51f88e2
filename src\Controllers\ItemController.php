<?php

namespace App\Controllers;

use App\Models\Item;
use App\Models\Category;
use App\Models\ItemImage;
use App\Services\ImageUploadService;
use Exception;

/**
 * Kontroler przedmiotów
 */
class ItemController
{
    private Item $itemModel;
    private Category $categoryModel;
    private ItemImage $imageModel;
    private ImageUploadService $uploadService;

    public function __construct()
    {
        $this->itemModel = new Item();
        $this->categoryModel = new Category();
        $this->imageModel = new ItemImage();
        $this->uploadService = new ImageUploadService();
    }

    /**
     * Lista przedmiotów
     */
    public function index(): void
    {
        try {
            // Sprawdzenie uprawnień
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            // Parametry paginacji i filtrów
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['items_per_page'];
            $offset = ($page - 1) * $limit;

            // Filtry
            $filters = [
                'category_id' => !empty($_GET['category']) ? (int)$_GET['category'] : null,
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
                'search' => !empty($_GET['search']) ? trim($_GET['search']) : null,
                'is_lendable' => isset($_GET['lendable']) ? (bool)$_GET['lendable'] : null
            ];

            // Pobranie danych
            $items = $this->itemModel->getAll($limit, $offset, $filters);
            $totalItems = $this->itemModel->count($filters);
            $categories = $this->categoryModel->getAll();

            // Paginacja
            $pagination = paginate($totalItems, $page, $limit, '/items');

            $pageTitle = 'Przedmioty';
            include TEMPLATES_PATH . '/items/index.php';

        } catch (Exception $e) {
            logError('Błąd listy przedmiotów: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania przedmiotów');
            redirect('/dashboard');
        }
    }

    /**
     * Wyświetlenie szczegółów przedmiotu
     */
    public function show(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $item = $this->itemModel->getById($id);
            if (!$item) {
                flash('error', 'Przedmiot nie został znaleziony');
                redirect('/items');
            }

            // Pobranie zdjęć
            $images = $this->imageModel->getByItemId($id);

            // Pobranie historii (dla administratorów)
            $history = [];
            if ($authController->hasRole('admin')) {
                $history = $this->getItemHistory($id);
            }

            $pageTitle = $item['name'];
            include TEMPLATES_PATH . '/items/show.php';

        } catch (Exception $e) {
            logError('Błąd wyświetlania przedmiotu: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania przedmiotu');
            redirect('/items');
        }
    }

    /**
     * Formularz dodawania przedmiotu
     */
    public function create(): void
    {
        $authController = new AuthController();
        if (!$authController->hasPermission('manage_items')) {
            flash('error', 'Brak uprawnień');
            redirect('/items');
        }

        $categories = $this->categoryModel->getAll();
        $pageTitle = 'Dodaj przedmiot';
        include TEMPLATES_PATH . '/items/create.php';
    }

    /**
     * Zapisanie nowego przedmiotu
     */
    public function store(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                flash('error', 'Brak uprawnień');
                redirect('/items');
            }

            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'details' => trim($_POST['details'] ?? ''),
                'category_id' => (int)($_POST['category_id'] ?? 0),
                'is_lendable' => isset($_POST['is_lendable']),
                'status' => $_POST['status'] ?? 'available'
            ];

            $itemId = $this->itemModel->create($data);

            // Upload zdjęć jeśli zostały przesłane
            if (!empty($_FILES['images']['name'][0])) {
                $uploadResult = $this->uploadService->uploadMultiple($_FILES['images'], $itemId);
                
                if (!empty($uploadResult['errors'])) {
                    foreach ($uploadResult['errors'] as $error) {
                        flash('warning', $error);
                    }
                }
                
                if ($uploadResult['success_count'] > 0) {
                    flash('success', "Przesłano {$uploadResult['success_count']} zdjęć");
                }
            }

            flash('success', 'Przedmiot został dodany pomyślnie');
            redirect("/items/{$itemId}");

        } catch (Exception $e) {
            logError('Błąd dodawania przedmiotu: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect('/items/create');
        }
    }

    /**
     * Formularz edycji przedmiotu
     */
    public function edit(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                flash('error', 'Brak uprawnień');
                redirect('/items');
            }

            $item = $this->itemModel->getById($id);
            if (!$item) {
                flash('error', 'Przedmiot nie został znaleziony');
                redirect('/items');
            }

            $categories = $this->categoryModel->getAll();
            $images = $this->imageModel->getByItemId($id);

            $pageTitle = 'Edytuj: ' . $item['name'];
            include TEMPLATES_PATH . '/items/edit.php';

        } catch (Exception $e) {
            logError('Błąd edycji przedmiotu: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania przedmiotu');
            redirect('/items');
        }
    }

    /**
     * Aktualizacja przedmiotu
     */
    public function update(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                flash('error', 'Brak uprawnień');
                redirect('/items');
            }

            $data = [
                'name' => trim($_POST['name'] ?? ''),
                'description' => trim($_POST['description'] ?? ''),
                'details' => trim($_POST['details'] ?? ''),
                'category_id' => (int)($_POST['category_id'] ?? 0),
                'is_lendable' => isset($_POST['is_lendable']),
                'status' => $_POST['status'] ?? 'available'
            ];

            $this->itemModel->update($id, $data);

            flash('success', 'Przedmiot został zaktualizowany pomyślnie');
            redirect("/items/{$id}");

        } catch (Exception $e) {
            logError('Błąd aktualizacji przedmiotu: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect("/items/{$id}/edit");
        }
    }

    /**
     * Usunięcie przedmiotu
     */
    public function delete(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/items');
                }
            }

            // Usunięcie zdjęć
            $this->imageModel->deleteByItemId($id);

            // Usunięcie przedmiotu
            $this->itemModel->delete($id);

            if (isAjax()) {
                jsonResponse(['success' => true, 'message' => 'Przedmiot został usunięty']);
            } else {
                flash('success', 'Przedmiot został usunięty pomyślnie');
                redirect('/items');
            }

        } catch (Exception $e) {
            logError('Błąd usuwania przedmiotu: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/items');
            }
        }
    }

    /**
     * Upload zdjęć
     */
    public function uploadImages(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                jsonResponse(['error' => 'Brak uprawnień'], 403);
            }

            if (empty($_FILES['images']['name'][0])) {
                jsonResponse(['error' => 'Nie wybrano plików'], 400);
            }

            $uploadResult = $this->uploadService->uploadMultiple($_FILES['images'], $id);

            jsonResponse([
                'success' => true,
                'uploaded_count' => $uploadResult['success_count'],
                'errors' => $uploadResult['errors'],
                'message' => "Przesłano {$uploadResult['success_count']} zdjęć"
            ]);

        } catch (Exception $e) {
            logError('Błąd uploadu zdjęć: ' . $e->getMessage());
            jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Usunięcie zdjęcia
     */
    public function deleteImage(int $imageId): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_items')) {
                jsonResponse(['error' => 'Brak uprawnień'], 403);
            }

            $this->imageModel->delete($imageId);

            jsonResponse(['success' => true, 'message' => 'Zdjęcie zostało usunięte']);

        } catch (Exception $e) {
            logError('Błąd usuwania zdjęcia: ' . $e->getMessage());
            jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Pobranie historii przedmiotu
     */
    private function getItemHistory(int $itemId): array
    {
        $query = "SELECT 
                    t.transfer_date,
                    t.transfer_type,
                    t.notes,
                    CONCAT(from_user.first_name, ' ', from_user.last_name) as from_user_name,
                    CONCAT(to_user.first_name, ' ', to_user.last_name) as to_user_name,
                    CONCAT(processed_by.first_name, ' ', processed_by.last_name) as processed_by_name
                  FROM transfers t
                  LEFT JOIN users from_user ON t.from_user_id = from_user.id
                  LEFT JOIN users to_user ON t.to_user_id = to_user.id
                  LEFT JOIN users processed_by ON t.processed_by_id = processed_by.id
                  WHERE t.item_id = :item_id
                  ORDER BY t.transfer_date DESC, t.created_at DESC
                  LIMIT 20";

        return DB->select($query, ['item_id' => $itemId]);
    }
}
