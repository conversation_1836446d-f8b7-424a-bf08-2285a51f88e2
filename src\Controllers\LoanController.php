<?php

namespace App\Controllers;

use App\Models\Loan;
use App\Models\Item;
use App\Models\User;
use App\Models\Reservation;
use Exception;

/**
 * Kontroler wypożyczeń
 */
class LoanController
{
    private Loan $loanModel;
    private Item $itemModel;
    private User $userModel;
    private Reservation $reservationModel;

    public function __construct()
    {
        $this->loanModel = new Loan();
        $this->itemModel = new Item();
        $this->userModel = new User();
        $this->reservationModel = new Reservation();
    }

    /**
     * Lista wypożyczeń
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            // Parametry paginacji i filtrów
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['items_per_page'];
            $offset = ($page - 1) * $limit;

            // Filtry
            $filters = [
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
                'date_from' => !empty($_GET['date_from']) ? $_GET['date_from'] : null,
                'date_to' => !empty($_GET['date_to']) ? $_GET['date_to'] : null,
            ];

            // Dla geodetów - tylko własne wypożyczenia
            if (!$isAdmin) {
                $filters['borrower_id'] = $currentUser['id'];
            }

            // Pobranie danych
            $loans = $this->loanModel->getAll($limit, $offset, $filters);
            $totalLoans = $this->loanModel->count($filters);

            // Lista użytkowników (dla administratorów)
            $users = $isAdmin ? $this->userModel->getByRole('geodeta') : [];

            // Paginacja
            $pagination = paginate($totalLoans, $page, $limit, '/loans');

            $pageTitle = $isAdmin ? 'Wszystkie wypożyczenia' : 'Moje wypożyczenia';
            include TEMPLATES_PATH . '/loans/index.php';

        } catch (Exception $e) {
            logError('Błąd listy wypożyczeń: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania wypożyczeń');
            redirect('/dashboard');
        }
    }

    /**
     * Formularz nowego wypożyczenia
     */
    public function create(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                flash('error', 'Brak uprawnień');
                redirect('/loans');
            }

            // Dostępne przedmioty
            $availableItems = $this->itemModel->getAvailable();
            
            // Lista użytkowników
            $users = $this->userModel->getByRole('geodeta');

            // Zatwierdzone rezerwacje
            $approvedReservations = $this->reservationModel->getAll(50, 0, ['status' => 'approved']);

            $pageTitle = 'Nowe wypożyczenie';
            include TEMPLATES_PATH . '/loans/create.php';

        } catch (Exception $e) {
            logError('Błąd formularza wypożyczenia: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania formularza');
            redirect('/loans');
        }
    }

    /**
     * Utworzenie wypożyczenia
     */
    public function store(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                flash('error', 'Brak uprawnień');
                redirect('/loans');
            }

            $currentUser = $authController->getCurrentUser();

            $data = [
                'item_id' => (int)($_POST['item_id'] ?? 0),
                'borrower_id' => (int)($_POST['borrower_id'] ?? 0),
                'lender_id' => $currentUser['id'],
                'reservation_id' => !empty($_POST['reservation_id']) ? (int)$_POST['reservation_id'] : null,
                'loan_date' => $_POST['loan_date'] ?? date('Y-m-d'),
                'expected_return_date' => !empty($_POST['expected_return_date']) ? $_POST['expected_return_date'] : null,
                'notes' => trim($_POST['notes'] ?? '')
            ];

            $loanId = $this->loanModel->create($data);

            flash('success', 'Wypożyczenie zostało utworzone pomyślnie');
            redirect("/loans/{$loanId}");

        } catch (Exception $e) {
            logError('Błąd tworzenia wypożyczenia: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect('/loans/create');
        }
    }

    /**
     * Szczegóły wypożyczenia
     */
    public function show(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            $loan = $this->loanModel->getById($id);
            if (!$loan) {
                flash('error', 'Wypożyczenie nie zostało znalezione');
                redirect('/loans');
            }

            // Sprawdzenie uprawnień - geodeci widzą tylko swoje wypożyczenia
            if (!$isAdmin && $loan['borrower_id'] != $currentUser['id']) {
                flash('error', 'Brak uprawnień do wyświetlenia tego wypożyczenia');
                redirect('/loans');
            }

            $pageTitle = 'Wypożyczenie: ' . $loan['item_name'];
            include TEMPLATES_PATH . '/loans/show.php';

        } catch (Exception $e) {
            logError('Błąd wyświetlania wypożyczenia: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania wypożyczenia');
            redirect('/loans');
        }
    }

    /**
     * Zwrot wypożyczenia
     */
    public function return(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/loans');
                }
            }

            $currentUser = $authController->getCurrentUser();
            $notes = trim($_POST['notes'] ?? '');

            $this->loanModel->returnLoan($id, $currentUser['id'], $notes);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Przedmiot został zwrócony pomyślnie'
                ]);
            } else {
                flash('success', 'Przedmiot został zwrócony pomyślnie');
                redirect("/loans/{$id}");
            }

        } catch (Exception $e) {
            logError('Błąd zwrotu wypożyczenia: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect("/loans/{$id}");
            }
        }
    }

    /**
     * Przedłużenie wypożyczenia
     */
    public function extend(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/loans');
                }
            }

            $newReturnDate = $_POST['new_return_date'] ?? '';
            $notes = trim($_POST['notes'] ?? '');

            $this->loanModel->extend($id, $newReturnDate, $notes);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Wypożyczenie zostało przedłużone'
                ]);
            } else {
                flash('success', 'Wypożyczenie zostało przedłużone');
                redirect("/loans/{$id}");
            }

        } catch (Exception $e) {
            logError('Błąd przedłużania wypożyczenia: ' . $e->getMessage());

            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect("/loans/{$id}");
            }
        }
    }
}
