<?php

namespace App\Controllers;

use App\Models\Item;
use App\Models\Loan;
use App\Models\User;
use App\Models\Category;
use App\Services\ReportService;
use Exception;

/**
 * Kontroler raportów
 */
class ReportController
{
    private Item $itemModel;
    private Loan $loanModel;
    private User $userModel;
    private Category $categoryModel;
    private ReportService $reportService;

    public function __construct()
    {
        $this->itemModel = new Item();
        $this->loanModel = new Loan();
        $this->userModel = new User();
        $this->categoryModel = new Category();
        $this->reportService = new ReportService();
    }

    /**
     * Strona główna raportów
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            // Podstawowe statystyki
            $stats = $this->getBasicStats($currentUser['id'], $isAdmin);

            // Ostatnie raporty (dla administratorów)
            $recentReports = [];
            if ($isAdmin) {
                $recentReports = $this->getRecentReports();
            }

            $pageTitle = 'Raporty';
            include TEMPLATES_PATH . '/reports/index.php';

        } catch (Exception $e) {
            logError('Błąd raportów: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania raportów');
            redirect('/dashboard');
        }
    }

    /**
     * Raport stanu magazynu
     */
    public function inventory(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('view_all_reports')) {
                flash('error', 'Brak uprawnień');
                redirect('/reports');
            }

            // Parametry filtrów
            $filters = [
                'category_id' => !empty($_GET['category']) ? (int)$_GET['category'] : null,
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
                'search' => !empty($_GET['search']) ? trim($_GET['search']) : null
            ];

            // Pobranie danych
            $inventoryData = $this->reportService->getInventoryReport($filters);
            $categories = $this->categoryModel->getAll();
            $categoryStats = $this->categoryModel->getStats();

            $pageTitle = 'Raport stanu magazynu';
            include TEMPLATES_PATH . '/reports/inventory.php';

        } catch (Exception $e) {
            logError('Błąd raportu magazynu: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas generowania raportu');
            redirect('/reports');
        }
    }

    /**
     * Raport wypożyczeń
     */
    public function loans(): void
    {
        try {
            $authController = new AuthController();
            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            if (!$isAdmin && !$authController->hasPermission('view_own_reports')) {
                flash('error', 'Brak uprawnień');
                redirect('/reports');
            }

            // Parametry filtrów
            $filters = [
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
                'date_from' => !empty($_GET['date_from']) ? $_GET['date_from'] : null,
                'date_to' => !empty($_GET['date_to']) ? $_GET['date_to'] : null,
                'user_id' => null
            ];

            // Dla geodetów - tylko własne wypożyczenia
            if (!$isAdmin) {
                $filters['user_id'] = $currentUser['id'];
            } elseif (!empty($_GET['user_id'])) {
                $filters['user_id'] = (int)$_GET['user_id'];
            }

            // Paginacja
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['reports_per_page'];
            $offset = ($page - 1) * $limit;

            // Pobranie danych
            $loans = $this->loanModel->getAll($limit, $offset, $filters);
            $totalLoans = $this->loanModel->count($filters);
            $loanStats = $this->reportService->getLoanStats($filters);

            // Lista użytkowników (dla administratorów)
            $users = $isAdmin ? $this->userModel->getByRole('geodeta') : [];

            // Paginacja
            $pagination = paginate($totalLoans, $page, $limit, '/reports/loans');

            $pageTitle = 'Raport wypożyczeń';
            include TEMPLATES_PATH . '/reports/loans.php';

        } catch (Exception $e) {
            logError('Błąd raportu wypożyczeń: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas generowania raportu');
            redirect('/reports');
        }
    }

    /**
     * Raport użytkowników
     */
    public function users(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('view_all_reports')) {
                flash('error', 'Brak uprawnień');
                redirect('/reports');
            }

            // Pobranie danych
            $userStats = $this->reportService->getUserStats();
            $topBorrowers = $this->reportService->getTopBorrowers();
            $overdueUsers = $this->reportService->getUsersWithOverdueLoans();

            $pageTitle = 'Raport użytkowników';
            include TEMPLATES_PATH . '/reports/users.php';

        } catch (Exception $e) {
            logError('Błąd raportu użytkowników: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas generowania raportu');
            redirect('/reports');
        }
    }

    /**
     * Historia przedmiotu
     */
    public function itemHistory(int $itemId): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('view_all_reports')) {
                flash('error', 'Brak uprawnień');
                redirect('/reports');
            }

            $item = $this->itemModel->getById($itemId);
            if (!$item) {
                flash('error', 'Przedmiot nie został znaleziony');
                redirect('/reports');
            }

            // Pobranie historii
            $history = $this->reportService->getItemHistory($itemId);
            $stats = $this->reportService->getItemStats($itemId);

            $pageTitle = 'Historia: ' . $item['name'];
            include TEMPLATES_PATH . '/reports/item_history.php';

        } catch (Exception $e) {
            logError('Błąd historii przedmiotu: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania historii');
            redirect('/reports');
        }
    }

    /**
     * Eksport raportu
     */
    public function export(string $type): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('view_all_reports')) {
                jsonResponse(['error' => 'Brak uprawnień'], 403);
            }

            $format = $_GET['format'] ?? 'csv';
            $filters = $_GET['filters'] ?? [];

            switch ($type) {
                case 'inventory':
                    $data = $this->reportService->getInventoryReport($filters);
                    $filename = 'raport_magazyn_' . date('Y-m-d');
                    break;

                case 'loans':
                    $data = $this->reportService->getLoansForExport($filters);
                    $filename = 'raport_wypozyczenia_' . date('Y-m-d');
                    break;

                case 'users':
                    $data = $this->reportService->getUsersForExport();
                    $filename = 'raport_uzytkownicy_' . date('Y-m-d');
                    break;

                default:
                    throw new Exception('Nieznany typ raportu');
            }

            if ($format === 'csv') {
                $this->exportToCsv($data, $filename);
            } elseif ($format === 'pdf') {
                $this->exportToPdf($data, $filename, $type);
            } else {
                throw new Exception('Nieobsługiwany format eksportu');
            }

        } catch (Exception $e) {
            logError('Błąd eksportu raportu: ' . $e->getMessage());
            jsonResponse(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Pobranie podstawowych statystyk
     */
    private function getBasicStats(int $userId, bool $isAdmin): array
    {
        if ($isAdmin) {
            return [
                'total_items' => $this->itemModel->count(),
                'available_items' => $this->itemModel->count(['status' => 'available']),
                'lent_items' => $this->itemModel->count(['status' => 'lent']),
                'active_loans' => $this->loanModel->count(['status' => 'active']),
                'overdue_loans' => $this->loanModel->count(['status' => 'overdue']),
                'total_users' => $this->userModel->countActive()
            ];
        } else {
            return [
                'my_active_loans' => $this->loanModel->count(['borrower_id' => $userId, 'status' => 'active']),
                'my_overdue_loans' => $this->loanModel->count(['borrower_id' => $userId, 'status' => 'overdue']),
                'available_items' => $this->itemModel->count(['status' => 'available', 'is_lendable' => true])
            ];
        }
    }

    /**
     * Pobranie ostatnich raportów
     */
    private function getRecentReports(): array
    {
        // Symulacja ostatnich raportów - w rzeczywistej aplikacji
        // można przechowywać historię generowanych raportów
        return [
            [
                'name' => 'Raport magazynu',
                'type' => 'inventory',
                'generated_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
                'generated_by' => 'Administrator'
            ],
            [
                'name' => 'Raport wypożyczeń',
                'type' => 'loans',
                'generated_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'generated_by' => 'Administrator'
            ]
        ];
    }

    /**
     * Eksport do CSV
     */
    private function exportToCsv(array $data, string $filename): void
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');

        $output = fopen('php://output', 'w');

        // BOM dla UTF-8
        fprintf($output, chr(0xEF) . chr(0xBB) . chr(0xBF));

        if (!empty($data)) {
            // Nagłówki
            fputcsv($output, array_keys($data[0]), ';');

            // Dane
            foreach ($data as $row) {
                fputcsv($output, $row, ';');
            }
        }

        fclose($output);
        exit;
    }

    /**
     * Eksport do PDF
     */
    private function exportToPdf(array $data, string $filename, string $type): void
    {
        // Implementacja eksportu PDF przy użyciu dompdf
        // Tutaj można dodać szczegółową implementację
        
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '.pdf"');
        
        // Placeholder - w rzeczywistej implementacji użyj dompdf
        echo "PDF export not implemented yet";
        exit;
    }
}
