<?php

namespace App\Controllers;

use App\Models\Reservation;
use App\Models\Item;
use App\Models\User;
use Exception;

/**
 * Kontroler rezerwacji
 */
class ReservationController
{
    private Reservation $reservationModel;
    private Item $itemModel;
    private User $userModel;

    public function __construct()
    {
        $this->reservationModel = new Reservation();
        $this->itemModel = new Item();
        $this->userModel = new User();
    }

    /**
     * Lista rezerwacji
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            // Parametry paginacji i filtrów
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['items_per_page'];
            $offset = ($page - 1) * $limit;

            // Filtry
            $filters = [
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
                'date_from' => !empty($_GET['date_from']) ? $_GET['date_from'] : null,
                'date_to' => !empty($_GET['date_to']) ? $_GET['date_to'] : null,
            ];

            // Dla geodetów - tylko własne rezerwacje
            if (!$isAdmin) {
                $filters['user_id'] = $currentUser['id'];
            }

            // Pobranie danych
            $reservations = $this->reservationModel->getAll($limit, $offset, $filters);
            $totalReservations = $this->reservationModel->count($filters);

            // Paginacja
            $pagination = paginate($totalReservations, $page, $limit, '/reservations');

            $pageTitle = $isAdmin ? 'Wszystkie rezerwacje' : 'Moje rezerwacje';
            include TEMPLATES_PATH . '/reservations/index.php';

        } catch (Exception $e) {
            logError('Błąd listy rezerwacji: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania rezerwacji');
            redirect('/dashboard');
        }
    }

    /**
     * Utworzenie rezerwacji
     */
    public function store(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak autoryzacji'], 401);
                } else {
                    redirect('/login');
                }
            }

            $currentUser = $authController->getCurrentUser();

            $data = [
                'item_id' => (int)($_POST['item_id'] ?? 0),
                'user_id' => $currentUser['id'],
                'requested_date' => $_POST['requested_date'] ?? '',
                'notes' => trim($_POST['notes'] ?? '')
            ];

            $reservationId = $this->reservationModel->create($data);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Rezerwacja została złożona pomyślnie',
                    'reservation_id' => $reservationId
                ]);
            } else {
                flash('success', 'Rezerwacja została złożona pomyślnie');
                redirect('/reservations');
            }

        } catch (Exception $e) {
            logError('Błąd tworzenia rezerwacji: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                back();
            }
        }
    }

    /**
     * Zatwierdzenie rezerwacji
     */
    public function approve(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/reservations');
                }
            }

            $this->reservationModel->approve($id);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Rezerwacja została zatwierdzona'
                ]);
            } else {
                flash('success', 'Rezerwacja została zatwierdzona');
                redirect('/reservations');
            }

        } catch (Exception $e) {
            logError('Błąd zatwierdzania rezerwacji: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/reservations');
            }
        }
    }

    /**
     * Odrzucenie rezerwacji
     */
    public function reject(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/reservations');
                }
            }

            $reason = trim($_POST['reason'] ?? '');
            $this->reservationModel->reject($id, $reason);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Rezerwacja została odrzucona'
                ]);
            } else {
                flash('success', 'Rezerwacja została odrzucona');
                redirect('/reservations');
            }

        } catch (Exception $e) {
            logError('Błąd odrzucania rezerwacji: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/reservations');
            }
        }
    }

    /**
     * Anulowanie rezerwacji
     */
    public function cancel(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak autoryzacji'], 401);
                } else {
                    redirect('/login');
                }
            }

            $currentUser = $authController->getCurrentUser();
            $this->reservationModel->cancel($id, $currentUser['id']);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Rezerwacja została anulowana'
                ]);
            } else {
                flash('success', 'Rezerwacja została anulowana');
                redirect('/reservations');
            }

        } catch (Exception $e) {
            logError('Błąd anulowania rezerwacji: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/reservations');
            }
        }
    }
}
