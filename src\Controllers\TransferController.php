<?php

namespace App\Controllers;

use App\Models\TransferRequest;
use App\Models\Item;
use App\Models\User;
use Exception;

/**
 * Kontroler transferów
 */
class TransferController
{
    private TransferRequest $transferModel;
    private Item $itemModel;
    private User $userModel;

    public function __construct()
    {
        $this->transferModel = new TransferRequest();
        $this->itemModel = new Item();
        $this->userModel = new User();
    }

    /**
     * Lista transferów
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $isAdmin = $authController->hasRole('admin');

            // Parametry paginacji i filtrów
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['items_per_page'];
            $offset = ($page - 1) * $limit;

            // Filtry
            $filters = [
                'status' => !empty($_GET['status']) ? $_GET['status'] : null,
            ];

            // Dla geodetów - tylko własne prośby lub dotyczące ich przedmiotów
            if (!$isAdmin) {
                // Pobranie próśb użytkownika i próśb dotyczących jego przedmiotów
                $myRequests = $this->transferModel->getByRequester($currentUser['id']);
                $requestsForMyItems = $this->transferModel->getByHolder($currentUser['id']);
                
                $transfers = array_merge($myRequests, $requestsForMyItems);
                $totalTransfers = count($transfers);
                
                // Sortowanie i paginacja
                usort($transfers, function($a, $b) {
                    return strtotime($b['created_at']) - strtotime($a['created_at']);
                });
                
                $transfers = array_slice($transfers, $offset, $limit);
            } else {
                // Dla administratorów - wszystkie transfery
                $transfers = $this->transferModel->getAll($limit, $offset, $filters);
                $totalTransfers = $this->transferModel->count($filters);
            }

            // Paginacja
            $pagination = paginate($totalTransfers, $page, $limit, '/transfers');

            $pageTitle = 'Transfery przedmiotów';
            include TEMPLATES_PATH . '/transfers/index.php';

        } catch (Exception $e) {
            logError('Błąd listy transferów: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania transferów');
            redirect('/dashboard');
        }
    }

    /**
     * Prośba o transfer
     */
    public function requestTransfer(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak autoryzacji'], 401);
                } else {
                    redirect('/login');
                }
            }

            $currentUser = $authController->getCurrentUser();

            $data = [
                'item_id' => (int)($_POST['item_id'] ?? 0),
                'requester_id' => $currentUser['id'],
                'current_holder_id' => (int)($_POST['current_holder_id'] ?? 0),
                'message' => trim($_POST['message'] ?? '')
            ];

            $transferId = $this->transferModel->create($data);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Prośba o transfer została wysłana',
                    'transfer_id' => $transferId
                ]);
            } else {
                flash('success', 'Prośba o transfer została wysłana');
                redirect('/transfers');
            }

        } catch (Exception $e) {
            logError('Błąd prośby o transfer: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                back();
            }
        }
    }

    /**
     * Zatwierdzenie transferu
     */
    public function approve(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/transfers');
                }
            }

            $currentUser = $authController->getCurrentUser();
            $this->transferModel->approve($id, $currentUser['id']);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Transfer został zatwierdzony'
                ]);
            } else {
                flash('success', 'Transfer został zatwierdzony');
                redirect('/transfers');
            }

        } catch (Exception $e) {
            logError('Błąd zatwierdzania transferu: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/transfers');
            }
        }
    }

    /**
     * Odrzucenie transferu
     */
    public function reject(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/transfers');
                }
            }

            $currentUser = $authController->getCurrentUser();
            $reason = trim($_POST['reason'] ?? '');
            
            $this->transferModel->reject($id, $currentUser['id'], $reason);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Transfer został odrzucony'
                ]);
            } else {
                flash('success', 'Transfer został odrzucony');
                redirect('/transfers');
            }

        } catch (Exception $e) {
            logError('Błąd odrzucania transferu: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/transfers');
            }
        }
    }

    /**
     * Realizacja transferu
     */
    public function complete(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('approve_loans')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/transfers');
                }
            }

            $currentUser = $authController->getCurrentUser();
            $this->transferModel->complete($id, $currentUser['id']);

            if (isAjax()) {
                jsonResponse([
                    'success' => true,
                    'message' => 'Transfer został zrealizowany'
                ]);
            } else {
                flash('success', 'Transfer został zrealizowany');
                redirect('/transfers');
            }

        } catch (Exception $e) {
            logError('Błąd realizacji transferu: ' . $e->getMessage());
            
            if (isAjax()) {
                jsonResponse(['error' => $e->getMessage()], 400);
            } else {
                flash('error', $e->getMessage());
                redirect('/transfers');
            }
        }
    }
}
