<?php

namespace App\Controllers;

use App\Models\User;
use Exception;

/**
 * Kontroler użytkowników
 */
class UserController
{
    private User $userModel;

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Lista użytkowników
     */
    public function index(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/dashboard');
            }

            // Parametry paginacji i filtrów
            $page = max(1, (int)($_GET['page'] ?? 1));
            $limit = CONFIG['pagination']['items_per_page'];
            $offset = ($page - 1) * $limit;

            // Pobranie danych
            $users = $this->userModel->getAll($limit, $offset);
            $totalUsers = $this->userModel->count();

            // Paginacja
            $pagination = paginate($totalUsers, $page, $limit, '/users');

            $pageTitle = 'Użytkownicy';
            include TEMPLATES_PATH . '/users/index.php';

        } catch (Exception $e) {
            logError('Błąd listy użytkowników: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania użytkowników');
            redirect('/dashboard');
        }
    }

    /**
     * Profil użytkownika
     */
    public function profile(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $user = $this->userModel->getById($currentUser['id']);

            if (!$user) {
                flash('error', 'Użytkownik nie został znaleziony');
                redirect('/dashboard');
            }

            $pageTitle = 'Mój profil';
            include TEMPLATES_PATH . '/users/profile.php';

        } catch (Exception $e) {
            logError('Błąd profilu użytkownika: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania profilu');
            redirect('/dashboard');
        }
    }

    /**
     * Aktualizacja profilu
     */
    public function updateProfile(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();

            $data = [
                'first_name' => trim($_POST['first_name'] ?? ''),
                'last_name' => trim($_POST['last_name'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'username' => trim($_POST['username'] ?? '')
            ];

            $this->userModel->update($currentUser['id'], $data);

            // Aktualizacja danych w sesji
            $_SESSION['user']['first_name'] = $data['first_name'];
            $_SESSION['user']['last_name'] = $data['last_name'];
            $_SESSION['user']['email'] = $data['email'];
            $_SESSION['user']['username'] = $data['username'];
            $_SESSION['user']['full_name'] = $data['first_name'] . ' ' . $data['last_name'];

            flash('success', 'Profil został zaktualizowany pomyślnie');
            redirect('/profile');

        } catch (Exception $e) {
            logError('Błąd aktualizacji profilu: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect('/profile');
        }
    }

    /**
     * Zmiana hasła
     */
    public function changePassword(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->isLoggedIn()) {
                redirect('/login');
            }

            $currentUser = $authController->getCurrentUser();
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';

            // Walidacja
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                throw new Exception('Wszystkie pola są wymagane');
            }

            if ($newPassword !== $confirmPassword) {
                throw new Exception('Nowe hasła nie są identyczne');
            }

            if (strlen($newPassword) < CONFIG['security']['password_min_length']) {
                throw new Exception('Nowe hasło musi mieć co najmniej ' . CONFIG['security']['password_min_length'] . ' znaków');
            }

            // Sprawdzenie obecnego hasła
            $user = $this->userModel->getById($currentUser['id']);
            if (!$this->userModel->verifyPassword($currentPassword, $user['password_hash'])) {
                throw new Exception('Obecne hasło jest nieprawidłowe');
            }

            // Zmiana hasła
            $this->userModel->changePassword($currentUser['id'], $newPassword);

            flash('success', 'Hasło zostało zmienione pomyślnie');
            redirect('/profile');

        } catch (Exception $e) {
            logError('Błąd zmiany hasła: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect('/profile');
        }
    }

    /**
     * Formularz dodawania użytkownika
     */
    public function create(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/users');
            }

            $pageTitle = 'Dodaj użytkownika';
            include TEMPLATES_PATH . '/users/create.php';

        } catch (Exception $e) {
            logError('Błąd formularza użytkownika: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania formularza');
            redirect('/users');
        }
    }

    /**
     * Utworzenie użytkownika
     */
    public function store(): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/users');
            }

            $data = [
                'username' => trim($_POST['username'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'password' => $_POST['password'] ?? '',
                'first_name' => trim($_POST['first_name'] ?? ''),
                'last_name' => trim($_POST['last_name'] ?? ''),
                'role' => $_POST['role'] ?? 'geodeta',
                'is_active' => isset($_POST['is_active'])
            ];

            $userId = $this->userModel->create($data);

            flash('success', 'Użytkownik został dodany pomyślnie');
            redirect("/users/{$userId}");

        } catch (Exception $e) {
            logError('Błąd dodawania użytkownika: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect('/users/create');
        }
    }

    /**
     * Szczegóły użytkownika
     */
    public function show(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/users');
            }

            $user = $this->userModel->getById($id);
            if (!$user) {
                flash('error', 'Użytkownik nie został znaleziony');
                redirect('/users');
            }

            $pageTitle = 'Użytkownik: ' . $user['first_name'] . ' ' . $user['last_name'];
            include TEMPLATES_PATH . '/users/show.php';

        } catch (Exception $e) {
            logError('Błąd wyświetlania użytkownika: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania użytkownika');
            redirect('/users');
        }
    }

    /**
     * Edycja użytkownika
     */
    public function edit(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/users');
            }

            $user = $this->userModel->getById($id);
            if (!$user) {
                flash('error', 'Użytkownik nie został znaleziony');
                redirect('/users');
            }

            $pageTitle = 'Edytuj: ' . $user['first_name'] . ' ' . $user['last_name'];
            include TEMPLATES_PATH . '/users/edit.php';

        } catch (Exception $e) {
            logError('Błąd edycji użytkownika: ' . $e->getMessage());
            flash('error', 'Wystąpił błąd podczas ładowania użytkownika');
            redirect('/users');
        }
    }

    /**
     * Aktualizacja użytkownika
     */
    public function update(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                flash('error', 'Brak uprawnień');
                redirect('/users');
            }

            $data = [
                'username' => trim($_POST['username'] ?? ''),
                'email' => trim($_POST['email'] ?? ''),
                'first_name' => trim($_POST['first_name'] ?? ''),
                'last_name' => trim($_POST['last_name'] ?? ''),
                'role' => $_POST['role'] ?? 'geodeta',
                'is_active' => isset($_POST['is_active'])
            ];

            $this->userModel->update($id, $data);

            flash('success', 'Użytkownik został zaktualizowany pomyślnie');
            redirect("/users/{$id}");

        } catch (Exception $e) {
            logError('Błąd aktualizacji użytkownika: ' . $e->getMessage());
            flash('error', $e->getMessage());
            redirect("/users/{$id}/edit");
        }
    }

    /**
     * Usunięcie użytkownika
     */
    public function delete(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Brak uprawnień'], 403);
                } else {
                    flash('error', 'Brak uprawnień');
                    redirect('/users');
                }
            }

            // Sprawdzenie czy użytkownik istnieje
            $user = $this->userModel->getById($id);
            if (!$user) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Użytkownik nie został znaleziony'], 404);
                } else {
                    flash('error', 'Użytkownik nie został znaleziony');
                    redirect('/users');
                }
            }

            // Sprawdzenie czy użytkownik nie próbuje usunąć samego siebie
            if ($id === currentUser()['id']) {
                if (isAjax()) {
                    jsonResponse(['error' => 'Nie możesz usunąć własnego konta'], 400);
                } else {
                    flash('error', 'Nie możesz usunąć własnego konta');
                    redirect('/users');
                }
            }

            // Usunięcie użytkownika
            $this->userModel->delete($id);

            if (isAjax()) {
                jsonResponse(['success' => true, 'message' => 'Użytkownik został usunięty']);
            } else {
                flash('success', 'Użytkownik został usunięty pomyślnie');
                redirect('/users');
            }

        } catch (Exception $e) {
            logError('Błąd usuwania użytkownika: ' . $e->getMessage());
            if (isAjax()) {
                jsonResponse(['error' => 'Wystąpił błąd podczas usuwania użytkownika'], 500);
            } else {
                flash('error', 'Wystąpił błąd podczas usuwania użytkownika');
                redirect('/users');
            }
        }
    }

    /**
     * Resetowanie hasła użytkownika
     */
    public function resetPassword(int $id): void
    {
        try {
            $authController = new AuthController();
            if (!$authController->hasPermission('manage_users')) {
                jsonResponse(['error' => 'Brak uprawnień'], 403);
            }

            // Sprawdzenie czy użytkownik istnieje
            $user = $this->userModel->getById($id);
            if (!$user) {
                jsonResponse(['error' => 'Użytkownik nie został znaleziony'], 404);
            }

            // Generowanie nowego hasła
            $newPassword = $this->generateRandomPassword();

            // Aktualizacja hasła w bazie danych
            $this->userModel->changePassword($id, $newPassword);

            // TODO: Wysłanie emaila z nowym hasłem
            // W przyszłości można dodać funkcjonalność wysyłania emaili

            jsonResponse([
                'success' => true,
                'message' => 'Hasło zostało zresetowane',
                'new_password' => $newPassword // Tymczasowo zwracamy hasło w odpowiedzi
            ]);

        } catch (Exception $e) {
            logError('Błąd resetowania hasła: ' . $e->getMessage());
            jsonResponse(['error' => 'Wystąpił błąd podczas resetowania hasła'], 500);
        }
    }

    /**
     * Generowanie losowego hasła
     */
    private function generateRandomPassword(int $length = 12): string
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[random_int(0, strlen($characters) - 1)];
        }

        return $password;
    }
}
