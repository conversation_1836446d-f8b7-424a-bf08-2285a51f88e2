<?php

namespace App\Database;

use PDO;
use PDOException;
use Exception;

/**
 * Klasa do zarządzania połączeniem z bazą danych
 */
class Database
{
    private PDO $connection;
    private array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->connect();
    }

    /**
     * Nawiązanie połączenia z bazą danych
     */
    private function connect(): void
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%d;dbname=%s;charset=%s',
                $this->config['host'],
                $this->config['port'],
                $this->config['name'],
                $this->config['charset']
            );

            $this->connection = new PDO(
                $dsn,
                $this->config['username'],
                $this->config['password'],
                $this->config['options']
            );
        } catch (PDOException $e) {
            throw new Exception('Błąd połączenia z bazą danych: ' . $e->getMessage());
        }
    }

    /**
     * Pobranie instancji PDO
     */
    public function getConnection(): PDO
    {
        return $this->connection;
    }

    /**
     * Wykonanie zapytania SELECT
     */
    public function select(string $query, array $params = []): array
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('Błąd wykonania zapytania SELECT: ' . $e->getMessage());
        }
    }

    /**
     * Pobranie jednego rekordu
     */
    public function selectOne(string $query, array $params = []): ?array
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            $result = $stmt->fetch();
            return $result ?: null;
        } catch (PDOException $e) {
            throw new Exception('Błąd wykonania zapytania SELECT: ' . $e->getMessage());
        }
    }

    /**
     * Wykonanie zapytania INSERT
     */
    public function insert(string $query, array $params = []): int
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return (int) $this->connection->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception('Błąd wykonania zapytania INSERT: ' . $e->getMessage());
        }
    }

    /**
     * Wykonanie zapytania UPDATE
     */
    public function update(string $query, array $params = []): int
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception('Błąd wykonania zapytania UPDATE: ' . $e->getMessage());
        }
    }

    /**
     * Wykonanie zapytania DELETE
     */
    public function delete(string $query, array $params = []): int
    {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            throw new Exception('Błąd wykonania zapytania DELETE: ' . $e->getMessage());
        }
    }

    /**
     * Rozpoczęcie transakcji
     */
    public function beginTransaction(): bool
    {
        return $this->connection->beginTransaction();
    }

    /**
     * Zatwierdzenie transakcji
     */
    public function commit(): bool
    {
        return $this->connection->commit();
    }

    /**
     * Wycofanie transakcji
     */
    public function rollback(): bool
    {
        return $this->connection->rollback();
    }

    /**
     * Sprawdzenie czy jesteśmy w transakcji
     */
    public function inTransaction(): bool
    {
        return $this->connection->inTransaction();
    }

    /**
     * Wykonanie zapytania w transakcji
     */
    public function transaction(callable $callback)
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Sprawdzenie czy tabela istnieje
     */
    public function tableExists(string $tableName): bool
    {
        $query = "SHOW TABLES LIKE :table";
        $result = $this->selectOne($query, ['table' => $tableName]);
        return !empty($result);
    }

    /**
     * Pobranie liczby rekordów
     */
    public function count(string $table, string $where = '', array $params = []): int
    {
        $query = "SELECT COUNT(*) as count FROM {$table}";
        
        if (!empty($where)) {
            $query .= " WHERE {$where}";
        }
        
        $result = $this->selectOne($query, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Sprawdzenie czy rekord istnieje
     */
    public function exists(string $table, string $where, array $params = []): bool
    {
        return $this->count($table, $where, $params) > 0;
    }

    /**
     * Pobranie ostatniego ID
     */
    public function lastInsertId(): int
    {
        return (int) $this->connection->lastInsertId();
    }

    /**
     * Escape wartości dla bezpieczeństwa
     */
    public function quote(string $value): string
    {
        return $this->connection->quote($value);
    }
}
