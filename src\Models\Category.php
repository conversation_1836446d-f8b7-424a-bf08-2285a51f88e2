<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model kategorii przedmiotów
 */
class Category
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich kategorii
     */
    public function getAll(): array
    {
        $query = "SELECT id, name, description, is_lendable, created_at,
                         (SELECT COUNT(*) FROM items WHERE category_id = categories.id) as items_count
                  FROM categories 
                  ORDER BY name";
        
        return $this->db->select($query);
    }

    /**
     * Pobranie kategorii po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT id, name, description, is_lendable, created_at,
                         (SELECT COUNT(*) FROM items WHERE category_id = categories.id) as items_count
                  FROM categories 
                  WHERE id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Pobranie kategorii po nazwie
     */
    public function getByName(string $name): ?array
    {
        $query = "SELECT id, name, description, is_lendable, created_at 
                  FROM categories 
                  WHERE name = :name";
        
        return $this->db->selectOne($query, ['name' => $name]);
    }

    /**
     * Utworzenie nowej kategorii
     */
    public function create(array $data): int
    {
        $this->validateCategoryData($data);
        
        // Sprawdzenie unikalności nazwy
        if ($this->getByName($data['name'])) {
            throw new Exception('Kategoria o tej nazwie już istnieje');
        }
        
        $query = "INSERT INTO categories (name, description, is_lendable) 
                  VALUES (:name, :description, :is_lendable)";
        
        $params = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'is_lendable' => $data['is_lendable'] ?? true
        ];
        
        return $this->db->insert($query, $params);
    }

    /**
     * Aktualizacja kategorii
     */
    public function update(int $id, array $data): bool
    {
        $category = $this->getById($id);
        if (!$category) {
            throw new Exception('Kategoria nie istnieje');
        }
        
        // Sprawdzenie unikalności nazwy (jeśli się zmieniła)
        if (isset($data['name']) && $data['name'] !== $category['name']) {
            if ($this->getByName($data['name'])) {
                throw new Exception('Kategoria o tej nazwie już istnieje');
            }
        }
        
        $fields = [];
        $params = ['id' => $id];
        
        $allowedFields = ['name', 'description', 'is_lendable'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return true; // Brak zmian
        }
        
        $query = "UPDATE categories SET " . implode(', ', $fields) . " WHERE id = :id";
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Usunięcie kategorii
     */
    public function delete(int $id): bool
    {
        // Sprawdzenie czy kategoria ma przypisane przedmioty
        $itemsCount = $this->db->count('items', 'category_id = :id', ['id' => $id]);
        
        if ($itemsCount > 0) {
            throw new Exception('Nie można usunąć kategorii, która ma przypisane przedmioty');
        }
        
        $query = "DELETE FROM categories WHERE id = :id";
        
        return $this->db->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Pobranie kategorii wypożyczalnych
     */
    public function getLendable(): array
    {
        $query = "SELECT id, name, description, is_lendable, created_at 
                  FROM categories 
                  WHERE is_lendable = 1
                  ORDER BY name";
        
        return $this->db->select($query);
    }

    /**
     * Pobranie kategorii niewypożyczalnych
     */
    public function getNonLendable(): array
    {
        $query = "SELECT id, name, description, is_lendable, created_at 
                  FROM categories 
                  WHERE is_lendable = 0
                  ORDER BY name";
        
        return $this->db->select($query);
    }

    /**
     * Liczba wszystkich kategorii
     */
    public function count(): int
    {
        return $this->db->count('categories');
    }

    /**
     * Sprawdzenie czy kategoria istnieje
     */
    public function exists(int $id): bool
    {
        return $this->db->exists('categories', 'id = :id', ['id' => $id]);
    }

    /**
     * Pobranie statystyk kategorii
     */
    public function getStats(): array
    {
        $query = "SELECT 
                    c.id,
                    c.name,
                    c.is_lendable,
                    COUNT(i.id) as total_items,
                    SUM(CASE WHEN i.status = 'available' THEN 1 ELSE 0 END) as available_items,
                    SUM(CASE WHEN i.status = 'lent' THEN 1 ELSE 0 END) as lent_items,
                    SUM(CASE WHEN i.status = 'reserved' THEN 1 ELSE 0 END) as reserved_items,
                    SUM(CASE WHEN i.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_items
                  FROM categories c
                  LEFT JOIN items i ON c.id = i.category_id
                  GROUP BY c.id, c.name, c.is_lendable
                  ORDER BY c.name";
        
        return $this->db->select($query);
    }

    /**
     * Walidacja danych kategorii
     */
    private function validateCategoryData(array $data): void
    {
        if (empty($data['name'])) {
            throw new Exception('Nazwa kategorii jest wymagana');
        }
        
        if (strlen($data['name']) < 2) {
            throw new Exception('Nazwa kategorii musi mieć co najmniej 2 znaki');
        }
        
        if (strlen($data['name']) > 100) {
            throw new Exception('Nazwa kategorii może mieć maksymalnie 100 znaków');
        }
        
        if (isset($data['description']) && strlen($data['description']) > 1000) {
            throw new Exception('Opis kategorii może mieć maksymalnie 1000 znaków');
        }
        
        if (isset($data['is_lendable']) && !is_bool($data['is_lendable'])) {
            throw new Exception('Pole "wypożyczalna" musi być wartością logiczną');
        }
    }
}
