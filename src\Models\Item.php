<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model przedmiotu
 */
class Item
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich przedmiotów z paginacją
     */
    public function getAll(int $limit = 20, int $offset = 0, array $filters = []): array
    {
        $query = "SELECT i.*, c.name as category_name, c.is_lendable as category_lendable,
                         CONCAT(u.first_name, ' ', u.last_name) as current_holder_name,
                         (SELECT filename FROM item_images WHERE item_id = i.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  LEFT JOIN users u ON i.current_holder_id = u.id";
        
        $params = [];
        $conditions = [];
        
        // Filtry
        if (!empty($filters['category_id'])) {
            $conditions[] = "i.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['status'])) {
            $conditions[] = "i.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $conditions[] = "(i.name LIKE :search OR i.description LIKE :search OR i.details LIKE :search)";
            $params['search'] = "%{$filters['search']}%";
        }
        
        if (!empty($filters['is_lendable'])) {
            $conditions[] = "i.is_lendable = :is_lendable";
            $params['is_lendable'] = $filters['is_lendable'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY i.name LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie przedmiotu po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT i.*, c.name as category_name, c.is_lendable as category_lendable,
                         CONCAT(u.first_name, ' ', u.last_name) as current_holder_name,
                         u.username as current_holder_username
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  LEFT JOIN users u ON i.current_holder_id = u.id
                  WHERE i.id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Utworzenie nowego przedmiotu
     */
    public function create(array $data): int
    {
        $this->validateItemData($data);
        
        $query = "INSERT INTO items (name, description, details, category_id, is_lendable, status) 
                  VALUES (:name, :description, :details, :category_id, :is_lendable, :status)";
        
        $params = [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'details' => $data['details'] ?? '',
            'category_id' => $data['category_id'],
            'is_lendable' => $data['is_lendable'] ?? true,
            'status' => $data['status'] ?? 'available'
        ];
        
        return $this->db->insert($query, $params);
    }

    /**
     * Aktualizacja przedmiotu
     */
    public function update(int $id, array $data): bool
    {
        $item = $this->getById($id);
        if (!$item) {
            throw new Exception('Przedmiot nie istnieje');
        }
        
        $fields = [];
        $params = ['id' => $id];
        
        $allowedFields = ['name', 'description', 'details', 'category_id', 'is_lendable', 'status'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return true; // Brak zmian
        }
        
        $query = "UPDATE items SET " . implode(', ', $fields) . " WHERE id = :id";
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Usunięcie przedmiotu
     */
    public function delete(int $id): bool
    {
        // Sprawdzenie czy przedmiot ma aktywne wypożyczenia
        $activeLoans = $this->db->count('loans', 'item_id = :id AND status = "active"', ['id' => $id]);
        
        if ($activeLoans > 0) {
            throw new Exception('Nie można usunąć przedmiotu z aktywnymi wypożyczeniami');
        }
        
        // Sprawdzenie czy przedmiot ma oczekujące rezerwacje
        $pendingReservations = $this->db->count('reservations', 'item_id = :id AND status = "pending"', ['id' => $id]);
        
        if ($pendingReservations > 0) {
            throw new Exception('Nie można usunąć przedmiotu z oczekującymi rezerwacjami');
        }
        
        $query = "DELETE FROM items WHERE id = :id";
        
        return $this->db->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Zmiana statusu przedmiotu
     */
    public function updateStatus(int $id, string $status, int $holderId = null): bool
    {
        $validStatuses = ['available', 'reserved', 'lent', 'maintenance'];
        
        if (!in_array($status, $validStatuses)) {
            throw new Exception('Nieprawidłowy status przedmiotu');
        }
        
        $query = "UPDATE items SET status = :status, current_holder_id = :holder_id WHERE id = :id";
        
        $params = [
            'id' => $id,
            'status' => $status,
            'holder_id' => $holderId
        ];
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Pobranie dostępnych przedmiotów
     */
    public function getAvailable(int $categoryId = null): array
    {
        $query = "SELECT i.*, c.name as category_name,
                         (SELECT filename FROM item_images WHERE item_id = i.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE i.status = 'available' AND i.is_lendable = 1";
        
        $params = [];
        
        if ($categoryId) {
            $query .= " AND i.category_id = :category_id";
            $params['category_id'] = $categoryId;
        }
        
        $query .= " ORDER BY i.name";
        
        return $this->db->select($query, $params);
    }

    /**
     * Wyszukiwanie przedmiotów
     */
    public function search(string $term, int $limit = 20): array
    {
        $query = "SELECT i.*, c.name as category_name,
                         (SELECT filename FROM item_images WHERE item_id = i.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE (i.name LIKE :term OR i.description LIKE :term OR i.details LIKE :term OR c.name LIKE :term)
                  ORDER BY i.name
                  LIMIT :limit";
        
        $params = [
            'term' => "%{$term}%",
            'limit' => $limit
        ];
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie przedmiotów według kategorii
     */
    public function getByCategory(int $categoryId, int $limit = null): array
    {
        $query = "SELECT i.*, c.name as category_name,
                         (SELECT filename FROM item_images WHERE item_id = i.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE i.category_id = :category_id
                  ORDER BY i.name";
        
        if ($limit) {
            $query .= " LIMIT :limit";
        }
        
        $params = ['category_id' => $categoryId];
        
        if ($limit) {
            $params['limit'] = $limit;
        }
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie przedmiotów użytkownika
     */
    public function getByUser(int $userId): array
    {
        $query = "SELECT i.*, c.name as category_name,
                         (SELECT filename FROM item_images WHERE item_id = i.id AND is_primary = 1 LIMIT 1) as primary_image
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE i.current_holder_id = :user_id
                  ORDER BY i.name";
        
        return $this->db->select($query, ['user_id' => $userId]);
    }

    /**
     * Liczba wszystkich przedmiotów
     */
    public function count(array $filters = []): int
    {
        $query = "SELECT COUNT(*) as count FROM items i";
        $params = [];
        $conditions = [];
        
        // Filtry
        if (!empty($filters['category_id'])) {
            $conditions[] = "i.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['status'])) {
            $conditions[] = "i.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $conditions[] = "(i.name LIKE :search OR i.description LIKE :search OR i.details LIKE :search)";
            $params['search'] = "%{$filters['search']}%";
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $result = $this->db->selectOne($query, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Sprawdzenie czy przedmiot istnieje
     */
    public function exists(int $id): bool
    {
        return $this->db->exists('items', 'id = :id', ['id' => $id]);
    }

    /**
     * Walidacja danych przedmiotu
     */
    private function validateItemData(array $data): void
    {
        if (empty($data['name'])) {
            throw new Exception('Nazwa przedmiotu jest wymagana');
        }
        
        if (strlen($data['name']) < 2) {
            throw new Exception('Nazwa przedmiotu musi mieć co najmniej 2 znaki');
        }
        
        if (strlen($data['name']) > 200) {
            throw new Exception('Nazwa przedmiotu może mieć maksymalnie 200 znaków');
        }
        
        if (empty($data['category_id'])) {
            throw new Exception('Kategoria jest wymagana');
        }
        
        // Sprawdzenie czy kategoria istnieje
        $categoryModel = new Category();
        if (!$categoryModel->exists($data['category_id'])) {
            throw new Exception('Wybrana kategoria nie istnieje');
        }
        
        if (isset($data['status']) && !in_array($data['status'], ['available', 'reserved', 'lent', 'maintenance'])) {
            throw new Exception('Nieprawidłowy status przedmiotu');
        }
    }
}
