<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model zd<PERSON> przedmiotów
 */
class ItemImage
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich zdj<PERSON>ć przedmiotu
     */
    public function getByItemId(int $itemId): array
    {
        $query = "SELECT id, item_id, filename, original_name, file_size, mime_type, is_primary, created_at
                  FROM item_images 
                  WHERE item_id = :item_id 
                  ORDER BY is_primary DESC, created_at ASC";
        
        return $this->db->select($query, ['item_id' => $itemId]);
    }

    /**
     * Pobranie głównego zdjęcia przedmiotu
     */
    public function getPrimaryByItemId(int $itemId): ?array
    {
        $query = "SELECT id, item_id, filename, original_name, file_size, mime_type, is_primary, created_at
                  FROM item_images 
                  WHERE item_id = :item_id AND is_primary = 1 
                  LIMIT 1";
        
        return $this->db->selectOne($query, ['item_id' => $itemId]);
    }

    /**
     * Pobranie zdjęcia po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT id, item_id, filename, original_name, file_size, mime_type, is_primary, created_at
                  FROM item_images 
                  WHERE id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Dodanie nowego zdjęcia
     */
    public function create(array $data): int
    {
        $this->validateImageData($data);
        
        $query = "INSERT INTO item_images (item_id, filename, original_name, file_size, mime_type, is_primary) 
                  VALUES (:item_id, :filename, :original_name, :file_size, :mime_type, :is_primary)";
        
        $params = [
            'item_id' => $data['item_id'],
            'filename' => $data['filename'],
            'original_name' => $data['original_name'],
            'file_size' => $data['file_size'],
            'mime_type' => $data['mime_type'],
            'is_primary' => $data['is_primary'] ?? false
        ];
        
        return $this->db->insert($query, $params);
    }

    /**
     * Usunięcie zdjęcia
     */
    public function delete(int $id): bool
    {
        $image = $this->getById($id);
        if (!$image) {
            throw new Exception('Zdjęcie nie istnieje');
        }
        
        // Usunięcie pliku z dysku
        $this->deleteImageFile($image['filename']);
        
        // Usunięcie z bazy danych
        $query = "DELETE FROM item_images WHERE id = :id";
        $result = $this->db->delete($query, ['id' => $id]) > 0;
        
        // Jeśli usunięte zdjęcie było główne, ustaw inne jako główne
        if ($result && $image['is_primary']) {
            $this->setFirstAsPrimary($image['item_id']);
        }
        
        return $result;
    }

    /**
     * Ustawienie zdjęcia jako główne
     */
    public function setPrimary(int $id): bool
    {
        $image = $this->getById($id);
        if (!$image) {
            throw new Exception('Zdjęcie nie istnieje');
        }
        
        return $this->db->transaction(function($db) use ($image, $id) {
            // Usunięcie flagi primary z innych zdjęć tego przedmiotu
            $db->update(
                "UPDATE item_images SET is_primary = 0 WHERE item_id = :item_id",
                ['item_id' => $image['item_id']]
            );
            
            // Ustawienie tego zdjęcia jako główne
            return $db->update(
                "UPDATE item_images SET is_primary = 1 WHERE id = :id",
                ['id' => $id]
            ) > 0;
        });
    }

    /**
     * Usunięcie wszystkich zdjęć przedmiotu
     */
    public function deleteByItemId(int $itemId): bool
    {
        $images = $this->getByItemId($itemId);
        
        // Usunięcie plików z dysku
        foreach ($images as $image) {
            $this->deleteImageFile($image['filename']);
        }
        
        // Usunięcie z bazy danych
        $query = "DELETE FROM item_images WHERE item_id = :item_id";
        
        return $this->db->delete($query, ['item_id' => $itemId]) > 0;
    }

    /**
     * Liczba zdjęć przedmiotu
     */
    public function countByItemId(int $itemId): int
    {
        return $this->db->count('item_images', 'item_id = :item_id', ['item_id' => $itemId]);
    }

    /**
     * Sprawdzenie czy przedmiot ma zdjęcia
     */
    public function hasImages(int $itemId): bool
    {
        return $this->countByItemId($itemId) > 0;
    }

    /**
     * Pobranie statystyk zdjęć
     */
    public function getStats(): array
    {
        $query = "SELECT 
                    COUNT(*) as total_images,
                    SUM(file_size) as total_size,
                    AVG(file_size) as avg_size,
                    COUNT(DISTINCT item_id) as items_with_images
                  FROM item_images";
        
        return $this->db->selectOne($query) ?? [];
    }

    /**
     * Ustawienie pierwszego zdjęcia jako główne
     */
    private function setFirstAsPrimary(int $itemId): void
    {
        $firstImage = $this->db->selectOne(
            "SELECT id FROM item_images WHERE item_id = :item_id ORDER BY created_at ASC LIMIT 1",
            ['item_id' => $itemId]
        );
        
        if ($firstImage) {
            $this->setPrimary($firstImage['id']);
        }
    }

    /**
     * Usunięcie pliku zdjęcia z dysku
     */
    private function deleteImageFile(string $filename): void
    {
        $filePath = UPLOAD_PATH . '/items/' . $filename;
        
        if (file_exists($filePath)) {
            unlink($filePath);
        }
        
        // Usunięcie miniatur
        $thumbnailSizes = CONFIG['upload']['thumbnails'];
        foreach ($thumbnailSizes as $size => $dimensions) {
            $thumbnailPath = UPLOAD_PATH . '/thumbnails/' . $size . '_' . $filename;
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
        }
    }

    /**
     * Walidacja danych zdjęcia
     */
    private function validateImageData(array $data): void
    {
        $required = ['item_id', 'filename', 'original_name', 'file_size', 'mime_type'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Pole {$field} jest wymagane");
            }
        }
        
        // Sprawdzenie czy przedmiot istnieje
        $itemModel = new Item();
        if (!$itemModel->exists($data['item_id'])) {
            throw new Exception('Przedmiot nie istnieje');
        }
        
        // Sprawdzenie typu MIME
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($data['mime_type'], $allowedTypes)) {
            throw new Exception('Nieprawidłowy typ pliku');
        }
        
        // Sprawdzenie rozmiaru pliku
        $maxSize = CONFIG['upload']['max_size'];
        if ($data['file_size'] > $maxSize) {
            throw new Exception('Plik jest za duży. Maksymalny rozmiar: ' . formatFileSize($maxSize));
        }
        
        // Sprawdzenie czy plik istnieje
        $filePath = UPLOAD_PATH . '/items/' . $data['filename'];
        if (!file_exists($filePath)) {
            throw new Exception('Plik nie istnieje na serwerze');
        }
    }

    /**
     * Pobranie URL zdjęcia
     */
    public function getImageUrl(string $filename, string $size = 'original'): string
    {
        if ($size === 'original') {
            return uploadUrl('items/' . $filename);
        } else {
            return uploadUrl('thumbnails/' . $size . '_' . $filename);
        }
    }

    /**
     * Sprawdzenie czy miniatura istnieje
     */
    public function thumbnailExists(string $filename, string $size): bool
    {
        $thumbnailPath = UPLOAD_PATH . '/thumbnails/' . $size . '_' . $filename;
        return file_exists($thumbnailPath);
    }
}
