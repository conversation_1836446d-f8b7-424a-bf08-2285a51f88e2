<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model wypożyczeń
 */
class Loan
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich wypożyczeń z paginacją
     */
    public function getAll(int $limit = 20, int $offset = 0, array $filters = []): array
    {
        $query = "SELECT l.*, 
                         i.name as item_name,
                         CONCAT(borrower.first_name, ' ', borrower.last_name) as borrower_name,
                         borrower.username as borrower_username,
                         CONCAT(lender.first_name, ' ', lender.last_name) as lender_name,
                         CONCAT(receiver.first_name, ' ', receiver.last_name) as receiver_name,
                         c.name as category_name,
                         CASE 
                            WHEN l.status = 'active' AND l.expected_return_date < CURDATE() THEN 'overdue'
                            ELSE l.status
                         END as computed_status
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  JOIN users borrower ON l.borrower_id = borrower.id
                  JOIN users lender ON l.lender_id = lender.id
                  LEFT JOIN users receiver ON l.return_receiver_id = receiver.id
                  LEFT JOIN categories c ON i.category_id = c.id";
        
        $params = [];
        $conditions = [];
        
        // Filtry
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'overdue') {
                $conditions[] = "l.status = 'active' AND l.expected_return_date < CURDATE()";
            } else {
                $conditions[] = "l.status = :status";
                $params['status'] = $filters['status'];
            }
        }
        
        if (!empty($filters['borrower_id'])) {
            $conditions[] = "l.borrower_id = :borrower_id";
            $params['borrower_id'] = $filters['borrower_id'];
        }
        
        if (!empty($filters['item_id'])) {
            $conditions[] = "l.item_id = :item_id";
            $params['item_id'] = $filters['item_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $conditions[] = "l.loan_date >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $conditions[] = "l.loan_date <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY l.loan_date DESC LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie wypożyczenia po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT l.*, 
                         i.name as item_name, i.status as item_status,
                         CONCAT(borrower.first_name, ' ', borrower.last_name) as borrower_name,
                         borrower.username as borrower_username, borrower.email as borrower_email,
                         CONCAT(lender.first_name, ' ', lender.last_name) as lender_name,
                         CONCAT(receiver.first_name, ' ', receiver.last_name) as receiver_name,
                         c.name as category_name,
                         r.id as reservation_id,
                         CASE 
                            WHEN l.status = 'active' AND l.expected_return_date < CURDATE() THEN 'overdue'
                            ELSE l.status
                         END as computed_status,
                         DATEDIFF(CURDATE(), l.expected_return_date) as days_overdue
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  JOIN users borrower ON l.borrower_id = borrower.id
                  JOIN users lender ON l.lender_id = lender.id
                  LEFT JOIN users receiver ON l.return_receiver_id = receiver.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  LEFT JOIN reservations r ON l.reservation_id = r.id
                  WHERE l.id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Utworzenie nowego wypożyczenia
     */
    public function create(array $data): int
    {
        $this->validateLoanData($data);
        
        return $this->db->transaction(function($db) use ($data) {
            // Sprawdzenie czy przedmiot jest dostępny
            $item = $db->selectOne("SELECT status, current_holder_id FROM items WHERE id = :id", ['id' => $data['item_id']]);
            
            if (!$item) {
                throw new Exception('Przedmiot nie istnieje');
            }
            
            if ($item['status'] === 'lent') {
                throw new Exception('Przedmiot jest już wypożyczony');
            }
            
            // Utworzenie wypożyczenia
            $query = "INSERT INTO loans (item_id, borrower_id, lender_id, reservation_id, loan_date, expected_return_date, status, notes) 
                      VALUES (:item_id, :borrower_id, :lender_id, :reservation_id, :loan_date, :expected_return_date, :status, :notes)";
            
            $params = [
                'item_id' => $data['item_id'],
                'borrower_id' => $data['borrower_id'],
                'lender_id' => $data['lender_id'],
                'reservation_id' => $data['reservation_id'] ?? null,
                'loan_date' => $data['loan_date'],
                'expected_return_date' => $data['expected_return_date'] ?? null,
                'status' => 'active',
                'notes' => $data['notes'] ?? ''
            ];
            
            $loanId = $db->insert($query, $params);
            
            // Aktualizacja statusu przedmiotu
            $db->update(
                "UPDATE items SET status = 'lent', current_holder_id = :holder_id WHERE id = :id",
                ['id' => $data['item_id'], 'holder_id' => $data['borrower_id']]
            );
            
            // Jeśli wypożyczenie jest na podstawie rezerwacji, oznacz ją jako zrealizowaną
            if (!empty($data['reservation_id'])) {
                $db->update(
                    "UPDATE reservations SET status = 'fulfilled' WHERE id = :id",
                    ['id' => $data['reservation_id']]
                );
            }
            
            // Dodanie wpisu do historii transferów
            $db->insert(
                "INSERT INTO transfers (item_id, from_user_id, to_user_id, transfer_type, processed_by_id, loan_id, transfer_date, notes) 
                 VALUES (:item_id, NULL, :to_user_id, 'loan', :processed_by_id, :loan_id, :transfer_date, :notes)",
                [
                    'item_id' => $data['item_id'],
                    'to_user_id' => $data['borrower_id'],
                    'processed_by_id' => $data['lender_id'],
                    'loan_id' => $loanId,
                    'transfer_date' => $data['loan_date'],
                    'notes' => 'Wypożyczenie przedmiotu'
                ]
            );
            
            return $loanId;
        });
    }

    /**
     * Zwrot wypożyczenia
     */
    public function returnLoan(int $id, int $receiverId, string $notes = ''): bool
    {
        $loan = $this->getById($id);
        if (!$loan) {
            throw new Exception('Wypożyczenie nie istnieje');
        }
        
        if ($loan['status'] !== 'active') {
            throw new Exception('Można zwrócić tylko aktywne wypożyczenia');
        }
        
        return $this->db->transaction(function($db) use ($id, $loan, $receiverId, $notes) {
            $returnDate = date('Y-m-d');
            
            // Aktualizacja wypożyczenia
            $db->update(
                "UPDATE loans SET status = 'returned', actual_return_date = :return_date, return_receiver_id = :receiver_id, notes = CONCAT(COALESCE(notes, ''), :notes) WHERE id = :id",
                [
                    'id' => $id,
                    'return_date' => $returnDate,
                    'receiver_id' => $receiverId,
                    'notes' => ($loan['notes'] ? "\n" : '') . "Zwrócono: " . $notes
                ]
            );
            
            // Aktualizacja statusu przedmiotu
            $db->update(
                "UPDATE items SET status = 'available', current_holder_id = NULL WHERE id = :id",
                ['id' => $loan['item_id']]
            );
            
            // Dodanie wpisu do historii transferów
            $db->insert(
                "INSERT INTO transfers (item_id, from_user_id, to_user_id, transfer_type, processed_by_id, loan_id, transfer_date, notes) 
                 VALUES (:item_id, :from_user_id, NULL, 'return', :processed_by_id, :loan_id, :transfer_date, :notes)",
                [
                    'item_id' => $loan['item_id'],
                    'from_user_id' => $loan['borrower_id'],
                    'processed_by_id' => $receiverId,
                    'loan_id' => $id,
                    'transfer_date' => $returnDate,
                    'notes' => 'Zwrot przedmiotu: ' . $notes
                ]
            );
            
            return true;
        });
    }

    /**
     * Przedłużenie wypożyczenia
     */
    public function extend(int $id, string $newReturnDate, string $notes = ''): bool
    {
        $loan = $this->getById($id);
        if (!$loan) {
            throw new Exception('Wypożyczenie nie istnieje');
        }
        
        if ($loan['status'] !== 'active') {
            throw new Exception('Można przedłużyć tylko aktywne wypożyczenia');
        }
        
        // Walidacja nowej daty
        $newDate = new \DateTime($newReturnDate);
        $currentDate = new \DateTime($loan['expected_return_date'] ?? 'today');
        
        if ($newDate <= $currentDate) {
            throw new Exception('Nowa data zwrotu musi być późniejsza niż obecna');
        }
        
        $updateNotes = ($loan['notes'] ? "\n" : '') . "Przedłużono do {$newReturnDate}: " . $notes;
        
        return $this->db->update(
            "UPDATE loans SET expected_return_date = :new_date, notes = :notes WHERE id = :id",
            [
                'id' => $id,
                'new_date' => $newReturnDate,
                'notes' => $updateNotes
            ]
        ) > 0;
    }

    /**
     * Pobranie wypożyczeń użytkownika
     */
    public function getByUser(int $userId, string $status = null): array
    {
        $query = "SELECT l.*, 
                         i.name as item_name,
                         c.name as category_name,
                         CASE 
                            WHEN l.status = 'active' AND l.expected_return_date < CURDATE() THEN 'overdue'
                            ELSE l.status
                         END as computed_status,
                         DATEDIFF(CURDATE(), l.expected_return_date) as days_overdue
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE l.borrower_id = :user_id";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            if ($status === 'overdue') {
                $query .= " AND l.status = 'active' AND l.expected_return_date < CURDATE()";
            } else {
                $query .= " AND l.status = :status";
                $params['status'] = $status;
            }
        }
        
        $query .= " ORDER BY l.loan_date DESC";
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie aktywnych wypożyczeń użytkownika
     */
    public function getActiveByUser(int $userId): array
    {
        return $this->getByUser($userId, 'active');
    }

    /**
     * Pobranie przeterminowanych wypożyczeń
     */
    public function getOverdue(): array
    {
        $query = "SELECT l.*, 
                         i.name as item_name,
                         CONCAT(borrower.first_name, ' ', borrower.last_name) as borrower_name,
                         borrower.email as borrower_email,
                         c.name as category_name,
                         DATEDIFF(CURDATE(), l.expected_return_date) as days_overdue
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  JOIN users borrower ON l.borrower_id = borrower.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE l.status = 'active' AND l.expected_return_date < CURDATE()
                  ORDER BY l.expected_return_date ASC";
        
        return $this->db->select($query);
    }

    /**
     * Pobranie wypożyczeń kończących się wkrótce
     */
    public function getExpiringSoon(int $days = 3): array
    {
        $query = "SELECT l.*, 
                         i.name as item_name,
                         CONCAT(borrower.first_name, ' ', borrower.last_name) as borrower_name,
                         borrower.email as borrower_email,
                         c.name as category_name,
                         DATEDIFF(l.expected_return_date, CURDATE()) as days_left
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  JOIN users borrower ON l.borrower_id = borrower.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE l.status = 'active' 
                  AND l.expected_return_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
                  ORDER BY l.expected_return_date ASC";
        
        return $this->db->select($query, ['days' => $days]);
    }

    /**
     * Liczba wypożyczeń
     */
    public function count(array $filters = []): int
    {
        $query = "SELECT COUNT(*) as count FROM loans l";
        $params = [];
        $conditions = [];
        
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'overdue') {
                $conditions[] = "l.status = 'active' AND l.expected_return_date < CURDATE()";
            } else {
                $conditions[] = "l.status = :status";
                $params['status'] = $filters['status'];
            }
        }
        
        if (!empty($filters['borrower_id'])) {
            $conditions[] = "l.borrower_id = :borrower_id";
            $params['borrower_id'] = $filters['borrower_id'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $result = $this->db->selectOne($query, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Sprawdzenie czy wypożyczenie istnieje
     */
    public function exists(int $id): bool
    {
        return $this->db->exists('loans', 'id = :id', ['id' => $id]);
    }

    /**
     * Walidacja danych wypożyczenia
     */
    private function validateLoanData(array $data): void
    {
        $required = ['item_id', 'borrower_id', 'lender_id', 'loan_date'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Pole {$field} jest wymagane");
            }
        }
        
        // Sprawdzenie dat
        $loanDate = new \DateTime($data['loan_date']);
        $today = new \DateTime('today');
        
        if ($loanDate > $today) {
            throw new Exception('Data wypożyczenia nie może być z przyszłości');
        }
        
        if (!empty($data['expected_return_date'])) {
            $returnDate = new \DateTime($data['expected_return_date']);
            if ($returnDate <= $loanDate) {
                throw new Exception('Data zwrotu musi być późniejsza niż data wypożyczenia');
            }
        }
        
        // Sprawdzenie czy użytkownicy istnieją
        $userModel = new User();
        if (!$userModel->getById($data['borrower_id'])) {
            throw new Exception('Wypożyczający nie istnieje');
        }
        
        if (!$userModel->getById($data['lender_id'])) {
            throw new Exception('Wydający nie istnieje');
        }
        
        // Sprawdzenie czy przedmiot istnieje
        $itemModel = new Item();
        if (!$itemModel->exists($data['item_id'])) {
            throw new Exception('Przedmiot nie istnieje');
        }
    }
}
