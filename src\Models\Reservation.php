<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model rezerwacji
 */
class Reservation
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich rezerwacji z paginacją
     */
    public function getAll(int $limit = 20, int $offset = 0, array $filters = []): array
    {
        $query = "SELECT r.*, 
                         i.name as item_name,
                         CONCAT(u.first_name, ' ', u.last_name) as user_name,
                         u.username,
                         c.name as category_name
                  FROM reservations r
                  JOIN items i ON r.item_id = i.id
                  JOIN users u ON r.user_id = u.id
                  LEFT JOIN categories c ON i.category_id = c.id";
        
        $params = [];
        $conditions = [];
        
        // Filtry
        if (!empty($filters['status'])) {
            $conditions[] = "r.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['user_id'])) {
            $conditions[] = "r.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['item_id'])) {
            $conditions[] = "r.item_id = :item_id";
            $params['item_id'] = $filters['item_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $conditions[] = "r.requested_date >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $conditions[] = "r.requested_date <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY r.created_at DESC LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie rezerwacji po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT r.*, 
                         i.name as item_name, i.status as item_status,
                         CONCAT(u.first_name, ' ', u.last_name) as user_name,
                         u.username, u.email,
                         c.name as category_name
                  FROM reservations r
                  JOIN items i ON r.item_id = i.id
                  JOIN users u ON r.user_id = u.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE r.id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Utworzenie nowej rezerwacji
     */
    public function create(array $data): int
    {
        $this->validateReservationData($data);
        
        // Sprawdzenie czy przedmiot jest dostępny
        $item = $this->db->selectOne("SELECT status, is_lendable FROM items WHERE id = :id", ['id' => $data['item_id']]);
        
        if (!$item) {
            throw new Exception('Przedmiot nie istnieje');
        }
        
        if (!$item['is_lendable']) {
            throw new Exception('Ten przedmiot nie jest wypożyczalny');
        }
        
        if ($item['status'] !== 'available') {
            throw new Exception('Przedmiot nie jest dostępny');
        }
        
        // Sprawdzenie czy użytkownik nie ma już rezerwacji tego przedmiotu
        $existingReservation = $this->db->selectOne(
            "SELECT id FROM reservations WHERE item_id = :item_id AND user_id = :user_id AND status IN ('pending', 'approved')",
            ['item_id' => $data['item_id'], 'user_id' => $data['user_id']]
        );
        
        if ($existingReservation) {
            throw new Exception('Masz już aktywną rezerwację tego przedmiotu');
        }
        
        $query = "INSERT INTO reservations (item_id, user_id, status, requested_date, notes) 
                  VALUES (:item_id, :user_id, :status, :requested_date, :notes)";
        
        $params = [
            'item_id' => $data['item_id'],
            'user_id' => $data['user_id'],
            'status' => $data['status'] ?? 'pending',
            'requested_date' => $data['requested_date'],
            'notes' => $data['notes'] ?? ''
        ];
        
        $reservationId = $this->db->insert($query, $params);
        
        // Zmiana statusu przedmiotu na zarezerwowany
        $this->db->update(
            "UPDATE items SET status = 'reserved' WHERE id = :id",
            ['id' => $data['item_id']]
        );
        
        return $reservationId;
    }

    /**
     * Aktualizacja rezerwacji
     */
    public function update(int $id, array $data): bool
    {
        $reservation = $this->getById($id);
        if (!$reservation) {
            throw new Exception('Rezerwacja nie istnieje');
        }
        
        $fields = [];
        $params = ['id' => $id];
        
        $allowedFields = ['status', 'requested_date', 'notes'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return true; // Brak zmian
        }
        
        $query = "UPDATE reservations SET " . implode(', ', $fields) . " WHERE id = :id";
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Zatwierdzenie rezerwacji
     */
    public function approve(int $id): bool
    {
        $reservation = $this->getById($id);
        if (!$reservation) {
            throw new Exception('Rezerwacja nie istnieje');
        }
        
        if ($reservation['status'] !== 'pending') {
            throw new Exception('Można zatwierdzić tylko oczekujące rezerwacje');
        }
        
        return $this->db->transaction(function($db) use ($id, $reservation) {
            // Aktualizacja statusu rezerwacji
            $db->update(
                "UPDATE reservations SET status = 'approved' WHERE id = :id",
                ['id' => $id]
            );
            
            // Przedmiot pozostaje zarezerwowany
            return true;
        });
    }

    /**
     * Odrzucenie rezerwacji
     */
    public function reject(int $id, string $reason = ''): bool
    {
        $reservation = $this->getById($id);
        if (!$reservation) {
            throw new Exception('Rezerwacja nie istnieje');
        }
        
        if ($reservation['status'] !== 'pending') {
            throw new Exception('Można odrzucić tylko oczekujące rezerwacje');
        }
        
        return $this->db->transaction(function($db) use ($id, $reservation, $reason) {
            // Aktualizacja statusu rezerwacji
            $notes = $reservation['notes'];
            if ($reason) {
                $notes .= ($notes ? "\n" : '') . "Powód odrzucenia: " . $reason;
            }
            
            $db->update(
                "UPDATE reservations SET status = 'rejected', notes = :notes WHERE id = :id",
                ['id' => $id, 'notes' => $notes]
            );
            
            // Zwolnienie przedmiotu
            $db->update(
                "UPDATE items SET status = 'available' WHERE id = :item_id",
                ['item_id' => $reservation['item_id']]
            );
            
            return true;
        });
    }

    /**
     * Anulowanie rezerwacji
     */
    public function cancel(int $id, int $userId): bool
    {
        $reservation = $this->getById($id);
        if (!$reservation) {
            throw new Exception('Rezerwacja nie istnieje');
        }
        
        if ($reservation['user_id'] != $userId) {
            throw new Exception('Możesz anulować tylko własne rezerwacje');
        }
        
        if (!in_array($reservation['status'], ['pending', 'approved'])) {
            throw new Exception('Nie można anulować tej rezerwacji');
        }
        
        return $this->db->transaction(function($db) use ($id, $reservation) {
            // Aktualizacja statusu rezerwacji
            $db->update(
                "UPDATE reservations SET status = 'cancelled' WHERE id = :id",
                ['id' => $id]
            );
            
            // Zwolnienie przedmiotu
            $db->update(
                "UPDATE items SET status = 'available' WHERE id = :item_id",
                ['item_id' => $reservation['item_id']]
            );
            
            return true;
        });
    }

    /**
     * Realizacja rezerwacji (przekształcenie w wypożyczenie)
     */
    public function fulfill(int $id): bool
    {
        $reservation = $this->getById($id);
        if (!$reservation) {
            throw new Exception('Rezerwacja nie istnieje');
        }
        
        if ($reservation['status'] !== 'approved') {
            throw new Exception('Można zrealizować tylko zatwierdzone rezerwacje');
        }
        
        return $this->db->update(
            "UPDATE reservations SET status = 'fulfilled' WHERE id = :id",
            ['id' => $id]
        ) > 0;
    }

    /**
     * Pobranie rezerwacji użytkownika
     */
    public function getByUser(int $userId, string $status = null): array
    {
        $query = "SELECT r.*, 
                         i.name as item_name,
                         c.name as category_name
                  FROM reservations r
                  JOIN items i ON r.item_id = i.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE r.user_id = :user_id";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            $query .= " AND r.status = :status";
            $params['status'] = $status;
        }
        
        $query .= " ORDER BY r.created_at DESC";
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie rezerwacji przedmiotu
     */
    public function getByItem(int $itemId): array
    {
        $query = "SELECT r.*, 
                         CONCAT(u.first_name, ' ', u.last_name) as user_name,
                         u.username
                  FROM reservations r
                  JOIN users u ON r.user_id = u.id
                  WHERE r.item_id = :item_id
                  ORDER BY r.created_at DESC";
        
        return $this->db->select($query, ['item_id' => $itemId]);
    }

    /**
     * Liczba rezerwacji
     */
    public function count(array $filters = []): int
    {
        $query = "SELECT COUNT(*) as count FROM reservations r";
        $params = [];
        $conditions = [];
        
        if (!empty($filters['status'])) {
            $conditions[] = "r.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['user_id'])) {
            $conditions[] = "r.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $result = $this->db->selectOne($query, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Sprawdzenie czy rezerwacja istnieje
     */
    public function exists(int $id): bool
    {
        return $this->db->exists('reservations', 'id = :id', ['id' => $id]);
    }

    /**
     * Walidacja danych rezerwacji
     */
    private function validateReservationData(array $data): void
    {
        $required = ['item_id', 'user_id', 'requested_date'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Pole {$field} jest wymagane");
            }
        }
        
        // Sprawdzenie czy data nie jest z przeszłości
        $requestedDate = new \DateTime($data['requested_date']);
        $today = new \DateTime('today');
        
        if ($requestedDate < $today) {
            throw new Exception('Data rezerwacji nie może być z przeszłości');
        }
        
        // Sprawdzenie czy użytkownik istnieje
        $userModel = new User();
        if (!$userModel->getById($data['user_id'])) {
            throw new Exception('Użytkownik nie istnieje');
        }
        
        // Sprawdzenie czy przedmiot istnieje
        $itemModel = new Item();
        if (!$itemModel->exists($data['item_id'])) {
            throw new Exception('Przedmiot nie istnieje');
        }
    }
}
