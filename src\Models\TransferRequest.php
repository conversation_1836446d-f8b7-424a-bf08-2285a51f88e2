<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model próśb o transfer
 */
class TransferRequest
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich próśb o transfer z paginacją
     */
    public function getAll(int $limit = 20, int $offset = 0, array $filters = []): array
    {
        $query = "SELECT tr.*, 
                         i.name as item_name,
                         CONCAT(requester.first_name, ' ', requester.last_name) as requester_name,
                         requester.username as requester_username,
                         CONCAT(holder.first_name, ' ', holder.last_name) as holder_name,
                         holder.username as holder_username,
                         CONCAT(approver.first_name, ' ', approver.last_name) as approver_name,
                         c.name as category_name
                  FROM transfer_requests tr
                  JOIN items i ON tr.item_id = i.id
                  JOIN users requester ON tr.requester_id = requester.id
                  JOIN users holder ON tr.current_holder_id = holder.id
                  LEFT JOIN users approver ON tr.approver_id = approver.id
                  LEFT JOIN categories c ON i.category_id = c.id";
        
        $params = [];
        $conditions = [];
        
        // Filtry
        if (!empty($filters['status'])) {
            $conditions[] = "tr.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['requester_id'])) {
            $conditions[] = "tr.requester_id = :requester_id";
            $params['requester_id'] = $filters['requester_id'];
        }
        
        if (!empty($filters['holder_id'])) {
            $conditions[] = "tr.current_holder_id = :holder_id";
            $params['holder_id'] = $filters['holder_id'];
        }
        
        if (!empty($filters['item_id'])) {
            $conditions[] = "tr.item_id = :item_id";
            $params['item_id'] = $filters['item_id'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY tr.created_at DESC LIMIT :limit OFFSET :offset";
        
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie prośby o transfer po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT tr.*, 
                         i.name as item_name, i.status as item_status,
                         CONCAT(requester.first_name, ' ', requester.last_name) as requester_name,
                         requester.username as requester_username, requester.email as requester_email,
                         CONCAT(holder.first_name, ' ', holder.last_name) as holder_name,
                         holder.username as holder_username, holder.email as holder_email,
                         CONCAT(approver.first_name, ' ', approver.last_name) as approver_name,
                         c.name as category_name
                  FROM transfer_requests tr
                  JOIN items i ON tr.item_id = i.id
                  JOIN users requester ON tr.requester_id = requester.id
                  JOIN users holder ON tr.current_holder_id = holder.id
                  LEFT JOIN users approver ON tr.approver_id = approver.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE tr.id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * Utworzenie nowej prośby o transfer
     */
    public function create(array $data): int
    {
        $this->validateTransferRequestData($data);
        
        // Sprawdzenie czy przedmiot jest wypożyczony przez wskazanego użytkownika
        $item = $this->db->selectOne(
            "SELECT current_holder_id, status FROM items WHERE id = :id",
            ['id' => $data['item_id']]
        );
        
        if (!$item) {
            throw new Exception('Przedmiot nie istnieje');
        }
        
        if ($item['status'] !== 'lent') {
            throw new Exception('Przedmiot nie jest wypożyczony');
        }
        
        if ($item['current_holder_id'] != $data['current_holder_id']) {
            throw new Exception('Przedmiot nie jest wypożyczony przez wskazanego użytkownika');
        }
        
        // Sprawdzenie czy użytkownik nie prosi o transfer od samego siebie
        if ($data['requester_id'] == $data['current_holder_id']) {
            throw new Exception('Nie możesz prosić o transfer od samego siebie');
        }
        
        // Sprawdzenie czy nie ma już aktywnej prośby o ten przedmiot
        $existingRequest = $this->db->selectOne(
            "SELECT id FROM transfer_requests 
             WHERE item_id = :item_id AND requester_id = :requester_id AND status = 'pending'",
            ['item_id' => $data['item_id'], 'requester_id' => $data['requester_id']]
        );
        
        if ($existingRequest) {
            throw new Exception('Masz już aktywną prośbę o transfer tego przedmiotu');
        }
        
        $query = "INSERT INTO transfer_requests (item_id, requester_id, current_holder_id, status, message) 
                  VALUES (:item_id, :requester_id, :current_holder_id, :status, :message)";
        
        $params = [
            'item_id' => $data['item_id'],
            'requester_id' => $data['requester_id'],
            'current_holder_id' => $data['current_holder_id'],
            'status' => 'pending',
            'message' => $data['message'] ?? ''
        ];
        
        return $this->db->insert($query, $params);
    }

    /**
     * Zatwierdzenie prośby o transfer
     */
    public function approve(int $id, int $approverId): bool
    {
        $request = $this->getById($id);
        if (!$request) {
            throw new Exception('Prośba o transfer nie istnieje');
        }
        
        if ($request['status'] !== 'pending') {
            throw new Exception('Można zatwierdzić tylko oczekujące prośby');
        }
        
        return $this->db->transaction(function($db) use ($id, $approverId) {
            // Aktualizacja statusu prośby
            $db->update(
                "UPDATE transfer_requests SET status = 'approved', approver_id = :approver_id, approved_at = NOW() WHERE id = :id",
                ['id' => $id, 'approver_id' => $approverId]
            );
            
            return true;
        });
    }

    /**
     * Odrzucenie prośby o transfer
     */
    public function reject(int $id, int $approverId, string $reason = ''): bool
    {
        $request = $this->getById($id);
        if (!$request) {
            throw new Exception('Prośba o transfer nie istnieje');
        }
        
        if ($request['status'] !== 'pending') {
            throw new Exception('Można odrzucić tylko oczekujące prośby');
        }
        
        $message = $request['message'];
        if ($reason) {
            $message .= ($message ? "\n" : '') . "Powód odrzucenia: " . $reason;
        }
        
        return $this->db->update(
            "UPDATE transfer_requests SET status = 'rejected', approver_id = :approver_id, message = :message WHERE id = :id",
            ['id' => $id, 'approver_id' => $approverId, 'message' => $message]
        ) > 0;
    }

    /**
     * Realizacja transferu
     */
    public function complete(int $id, int $processedById): bool
    {
        $request = $this->getById($id);
        if (!$request) {
            throw new Exception('Prośba o transfer nie istnieje');
        }
        
        if ($request['status'] !== 'approved') {
            throw new Exception('Można zrealizować tylko zatwierdzone prośby');
        }
        
        return $this->db->transaction(function($db) use ($id, $request, $processedById) {
            // Aktualizacja statusu prośby
            $db->update(
                "UPDATE transfer_requests SET status = 'completed', completed_at = NOW() WHERE id = :id",
                ['id' => $id]
            );
            
            // Aktualizacja właściciela przedmiotu
            $db->update(
                "UPDATE items SET current_holder_id = :new_holder_id WHERE id = :item_id",
                ['item_id' => $request['item_id'], 'new_holder_id' => $request['requester_id']]
            );
            
            // Aktualizacja aktywnego wypożyczenia
            $db->update(
                "UPDATE loans SET borrower_id = :new_borrower_id WHERE item_id = :item_id AND status = 'active'",
                ['item_id' => $request['item_id'], 'new_borrower_id' => $request['requester_id']]
            );
            
            // Dodanie wpisu do historii transferów
            $db->insert(
                "INSERT INTO transfers (item_id, from_user_id, to_user_id, transfer_type, processed_by_id, transfer_request_id, transfer_date, notes) 
                 VALUES (:item_id, :from_user_id, :to_user_id, 'transfer', :processed_by_id, :transfer_request_id, CURDATE(), :notes)",
                [
                    'item_id' => $request['item_id'],
                    'from_user_id' => $request['current_holder_id'],
                    'to_user_id' => $request['requester_id'],
                    'processed_by_id' => $processedById,
                    'transfer_request_id' => $id,
                    'notes' => 'Transfer między użytkownikami: ' . ($request['message'] ?: 'Brak opisu')
                ]
            );
            
            return true;
        });
    }

    /**
     * Anulowanie prośby o transfer
     */
    public function cancel(int $id, int $userId): bool
    {
        $request = $this->getById($id);
        if (!$request) {
            throw new Exception('Prośba o transfer nie istnieje');
        }
        
        if ($request['requester_id'] != $userId) {
            throw new Exception('Możesz anulować tylko własne prośby');
        }
        
        if ($request['status'] !== 'pending') {
            throw new Exception('Można anulować tylko oczekujące prośby');
        }
        
        return $this->db->update(
            "UPDATE transfer_requests SET status = 'cancelled' WHERE id = :id",
            ['id' => $id]
        ) > 0;
    }

    /**
     * Pobranie próśb użytkownika
     */
    public function getByRequester(int $userId, string $status = null): array
    {
        $query = "SELECT tr.*, 
                         i.name as item_name,
                         CONCAT(holder.first_name, ' ', holder.last_name) as holder_name,
                         c.name as category_name
                  FROM transfer_requests tr
                  JOIN items i ON tr.item_id = i.id
                  JOIN users holder ON tr.current_holder_id = holder.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE tr.requester_id = :user_id";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            $query .= " AND tr.status = :status";
            $params['status'] = $status;
        }
        
        $query .= " ORDER BY tr.created_at DESC";
        
        return $this->db->select($query, $params);
    }

    /**
     * Pobranie próśb dotyczących przedmiotów użytkownika
     */
    public function getByHolder(int $userId, string $status = null): array
    {
        $query = "SELECT tr.*, 
                         i.name as item_name,
                         CONCAT(requester.first_name, ' ', requester.last_name) as requester_name,
                         c.name as category_name
                  FROM transfer_requests tr
                  JOIN items i ON tr.item_id = i.id
                  JOIN users requester ON tr.requester_id = requester.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  WHERE tr.current_holder_id = :user_id";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            $query .= " AND tr.status = :status";
            $params['status'] = $status;
        }
        
        $query .= " ORDER BY tr.created_at DESC";
        
        return $this->db->select($query, $params);
    }

    /**
     * Liczba próśb o transfer
     */
    public function count(array $filters = []): int
    {
        $query = "SELECT COUNT(*) as count FROM transfer_requests tr";
        $params = [];
        $conditions = [];
        
        if (!empty($filters['status'])) {
            $conditions[] = "tr.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['requester_id'])) {
            $conditions[] = "tr.requester_id = :requester_id";
            $params['requester_id'] = $filters['requester_id'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $result = $this->db->selectOne($query, $params);
        return (int) ($result['count'] ?? 0);
    }

    /**
     * Sprawdzenie czy prośba istnieje
     */
    public function exists(int $id): bool
    {
        return $this->db->exists('transfer_requests', 'id = :id', ['id' => $id]);
    }

    /**
     * Walidacja danych prośby o transfer
     */
    private function validateTransferRequestData(array $data): void
    {
        $required = ['item_id', 'requester_id', 'current_holder_id'];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("Pole {$field} jest wymagane");
            }
        }
        
        // Sprawdzenie czy użytkownicy istnieją
        $userModel = new User();
        if (!$userModel->getById($data['requester_id'])) {
            throw new Exception('Użytkownik proszący nie istnieje');
        }
        
        if (!$userModel->getById($data['current_holder_id'])) {
            throw new Exception('Obecny posiadacz nie istnieje');
        }
        
        // Sprawdzenie czy przedmiot istnieje
        $itemModel = new Item();
        if (!$itemModel->exists($data['item_id'])) {
            throw new Exception('Przedmiot nie istnieje');
        }
    }
}
