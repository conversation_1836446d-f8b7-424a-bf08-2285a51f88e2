<?php

namespace App\Models;

use App\Database\Database;
use Exception;

/**
 * Model użytkownika
 */
class User
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Pobranie wszystkich użytkowników
     */
    public function getAll(int $limit = null, int $offset = 0): array
    {
        $query = "SELECT id, username, email, first_name, last_name, role, is_active, created_at 
                  FROM users 
                  ORDER BY last_name, first_name";
        
        if ($limit) {
            $query .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        return $this->db->select($query);
    }

    /**
     * Pobranie użytkownika po ID
     */
    public function getById(int $id): ?array
    {
        $query = "SELECT id, username, email, first_name, last_name, role, is_active, created_at, updated_at 
                  FROM users 
                  WHERE id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * <PERSON><PERSON><PERSON> użytkownika po nazwie użytkownika
     */
    public function getByUsername(string $username): ?array
    {
        $query = "SELECT id, username, email, password_hash, first_name, last_name, role, is_active, created_at 
                  FROM users 
                  WHERE username = :username";
        
        return $this->db->selectOne($query, ['username' => $username]);
    }

    /**
     * Pobranie użytkownika po email
     */
    public function getByEmail(string $email): ?array
    {
        $query = "SELECT id, username, email, password_hash, first_name, last_name, role, is_active, created_at 
                  FROM users 
                  WHERE email = :email";
        
        return $this->db->selectOne($query, ['email' => $email]);
    }

    /**
     * Utworzenie nowego użytkownika
     */
    public function create(array $data): int
    {
        $this->validateUserData($data);
        
        // Sprawdzenie unikalności username i email
        if ($this->getByUsername($data['username'])) {
            throw new Exception('Nazwa użytkownika już istnieje');
        }
        
        if ($this->getByEmail($data['email'])) {
            throw new Exception('Adres email już istnieje');
        }
        
        $query = "INSERT INTO users (username, email, password_hash, first_name, last_name, role, is_active) 
                  VALUES (:username, :email, :password_hash, :first_name, :last_name, :role, :is_active)";
        
        $params = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'role' => $data['role'] ?? 'geodeta',
            'is_active' => $data['is_active'] ?? true
        ];
        
        return $this->db->insert($query, $params);
    }

    /**
     * Aktualizacja użytkownika
     */
    public function update(int $id, array $data): bool
    {
        $user = $this->getById($id);
        if (!$user) {
            throw new Exception('Użytkownik nie istnieje');
        }
        
        // Sprawdzenie unikalności username i email (jeśli się zmieniły)
        if (isset($data['username']) && $data['username'] !== $user['username']) {
            if ($this->getByUsername($data['username'])) {
                throw new Exception('Nazwa użytkownika już istnieje');
            }
        }
        
        if (isset($data['email']) && $data['email'] !== $user['email']) {
            if ($this->getByEmail($data['email'])) {
                throw new Exception('Adres email już istnieje');
            }
        }
        
        $fields = [];
        $params = ['id' => $id];
        
        $allowedFields = ['username', 'email', 'first_name', 'last_name', 'role', 'is_active'];
        
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "{$field} = :{$field}";
                $params[$field] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return true; // Brak zmian
        }
        
        $query = "UPDATE users SET " . implode(', ', $fields) . " WHERE id = :id";
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Zmiana hasła użytkownika
     */
    public function changePassword(int $id, string $newPassword): bool
    {
        $query = "UPDATE users SET password_hash = :password_hash WHERE id = :id";
        
        $params = [
            'id' => $id,
            'password_hash' => password_hash($newPassword, PASSWORD_DEFAULT)
        ];
        
        return $this->db->update($query, $params) > 0;
    }

    /**
     * Usunięcie użytkownika
     */
    public function delete(int $id): bool
    {
        // Sprawdzenie czy użytkownik ma aktywne wypożyczenia
        $activeLoans = $this->db->count('loans', 'borrower_id = :id AND status = "active"', ['id' => $id]);
        
        if ($activeLoans > 0) {
            throw new Exception('Nie można usunąć użytkownika z aktywnymi wypożyczeniami');
        }
        
        $query = "DELETE FROM users WHERE id = :id";
        
        return $this->db->delete($query, ['id' => $id]) > 0;
    }

    /**
     * Weryfikacja hasła
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * Pobranie użytkowników według roli
     */
    public function getByRole(string $role): array
    {
        $query = "SELECT id, username, email, first_name, last_name, role, is_active, created_at 
                  FROM users 
                  WHERE role = :role AND is_active = 1
                  ORDER BY last_name, first_name";
        
        return $this->db->select($query, ['role' => $role]);
    }

    /**
     * Wyszukiwanie użytkowników
     */
    public function search(string $term, int $limit = 20): array
    {
        $query = "SELECT id, username, email, first_name, last_name, role, is_active 
                  FROM users 
                  WHERE (first_name LIKE :term OR last_name LIKE :term OR username LIKE :term OR email LIKE :term)
                  AND is_active = 1
                  ORDER BY last_name, first_name
                  LIMIT :limit";
        
        $params = [
            'term' => "%{$term}%",
            'limit' => $limit
        ];
        
        return $this->db->select($query, $params);
    }

    /**
     * Liczba wszystkich użytkowników
     */
    public function count(): int
    {
        return $this->db->count('users');
    }

    /**
     * Liczba aktywnych użytkowników
     */
    public function countActive(): int
    {
        return $this->db->count('users', 'is_active = 1');
    }

    /**
     * Walidacja danych użytkownika
     */
    private function validateUserData(array $data): void
    {
        $required = ['username', 'email', 'password', 'first_name', 'last_name'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception("Pole {$field} jest wymagane");
            }
        }
        
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Nieprawidłowy adres email');
        }
        
        if (strlen($data['password']) < CONFIG['security']['password_min_length']) {
            throw new Exception('Hasło musi mieć co najmniej ' . CONFIG['security']['password_min_length'] . ' znaków');
        }
        
        if (strlen($data['username']) < 3) {
            throw new Exception('Nazwa użytkownika musi mieć co najmniej 3 znaki');
        }
        
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            throw new Exception('Nazwa użytkownika może zawierać tylko litery, cyfry i podkreślenia');
        }
        
        if (isset($data['role']) && !in_array($data['role'], ['admin', 'geodeta'])) {
            throw new Exception('Nieprawidłowa rola użytkownika');
        }
    }
}
