<?php

namespace App\Services;

use App\Models\ItemImage;
use Exception;
use Intervention\Image\ImageManagerStatic as Image;

/**
 * Serwis do obsługi uploadów zdjęć
 */
class ImageUploadService
{
    private ItemImage $imageModel;
    private string $uploadPath;
    private string $thumbnailPath;
    private array $allowedTypes;
    private int $maxFileSize;
    private array $thumbnailSizes;

    public function __construct()
    {
        $this->imageModel = new ItemImage();
        $this->uploadPath = UPLOAD_PATH . '/items';
        $this->thumbnailPath = UPLOAD_PATH . '/thumbnails';
        $this->allowedTypes = CONFIG['upload']['allowed_types'];
        $this->maxFileSize = CONFIG['upload']['max_size'];
        $this->thumbnailSizes = CONFIG['upload']['thumbnails'];
        
        // Utworzenie katalogów jeśli nie istnieją
        $this->ensureDirectoriesExist();
    }

    /**
     * Upload pojedynczego pliku
     */
    public function uploadSingle(array $file, int $itemId, bool $isPrimary = false): int
    {
        $this->validateFile($file);
        
        // Generowanie unikalnej nazwy pliku
        $filename = $this->generateUniqueFilename($file['name']);
        
        // Przeniesienie pliku
        $targetPath = $this->uploadPath . '/' . $filename;
        if (!move_uploaded_file($file['tmp_name'], $targetPath)) {
            throw new Exception('Błąd podczas przesyłania pliku');
        }
        
        try {
            // Utworzenie miniatur
            $this->createThumbnails($filename);
            
            // Zapisanie w bazie danych
            $imageData = [
                'item_id' => $itemId,
                'filename' => $filename,
                'original_name' => $file['name'],
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'is_primary' => $isPrimary
            ];
            
            return $this->imageModel->create($imageData);
            
        } catch (Exception $e) {
            // Usunięcie pliku w przypadku błędu
            if (file_exists($targetPath)) {
                unlink($targetPath);
            }
            throw $e;
        }
    }

    /**
     * Upload wielu plików
     */
    public function uploadMultiple(array $files, int $itemId): array
    {
        $uploadedIds = [];
        $errors = [];
        
        // Sprawdzenie czy pierwszy plik ma być główny
        $hasExistingImages = $this->imageModel->hasImages($itemId);
        
        foreach ($files['name'] as $index => $name) {
            if (empty($name)) {
                continue; // Pomiń puste pliki
            }
            
            $file = [
                'name' => $files['name'][$index],
                'type' => $files['type'][$index],
                'tmp_name' => $files['tmp_name'][$index],
                'error' => $files['error'][$index],
                'size' => $files['size'][$index]
            ];
            
            try {
                $isPrimary = !$hasExistingImages && empty($uploadedIds);
                $imageId = $this->uploadSingle($file, $itemId, $isPrimary);
                $uploadedIds[] = $imageId;
                
            } catch (Exception $e) {
                $errors[] = "Błąd uploadu pliku {$name}: " . $e->getMessage();
            }
        }
        
        return [
            'uploaded_ids' => $uploadedIds,
            'errors' => $errors,
            'success_count' => count($uploadedIds),
            'error_count' => count($errors)
        ];
    }

    /**
     * Utworzenie miniatur
     */
    private function createThumbnails(string $filename): void
    {
        $sourcePath = $this->uploadPath . '/' . $filename;
        
        foreach ($this->thumbnailSizes as $size => $dimensions) {
            $thumbnailPath = $this->thumbnailPath . '/' . $size . '_' . $filename;
            
            try {
                $image = Image::make($sourcePath);
                
                // Proporcjonalne skalowanie z przycinaniem
                $image->fit($dimensions['width'], $dimensions['height'], function ($constraint) {
                    $constraint->upsize();
                });
                
                // Optymalizacja jakości
                $quality = $this->getQualityForSize($size);
                $image->save($thumbnailPath, $quality);
                
            } catch (Exception $e) {
                logError("Błąd tworzenia miniatury {$size} dla pliku {$filename}: " . $e->getMessage());
                // Nie przerywamy procesu - miniatura nie jest krytyczna
            }
        }
    }

    /**
     * Walidacja pliku
     */
    private function validateFile(array $file): void
    {
        // Sprawdzenie błędów uploadu
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception($this->getUploadErrorMessage($file['error']));
        }
        
        // Sprawdzenie rozmiaru
        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('Plik jest za duży. Maksymalny rozmiar: ' . formatFileSize($this->maxFileSize));
        }
        
        // Sprawdzenie typu MIME
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            throw new Exception('Nieprawidłowy typ pliku. Dozwolone: ' . implode(', ', $this->allowedTypes));
        }
        
        // Sprawdzenie rozszerzenia
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $this->allowedTypes)) {
            throw new Exception('Nieprawidłowe rozszerzenie pliku. Dozwolone: ' . implode(', ', $this->allowedTypes));
        }
        
        // Sprawdzenie czy to rzeczywiście obraz
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            throw new Exception('Plik nie jest prawidłowym obrazem');
        }
        
        // Sprawdzenie wymiarów (opcjonalnie)
        $maxWidth = 4000;
        $maxHeight = 4000;
        if ($imageInfo[0] > $maxWidth || $imageInfo[1] > $maxHeight) {
            throw new Exception("Obraz jest za duży. Maksymalne wymiary: {$maxWidth}x{$maxHeight}px");
        }
    }

    /**
     * Generowanie unikalnej nazwy pliku
     */
    private function generateUniqueFilename(string $originalName): string
    {
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        $basename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // Sanityzacja nazwy
        $basename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);
        $basename = substr($basename, 0, 50); // Ograniczenie długości
        
        // Dodanie timestamp i losowego ciągu
        $timestamp = time();
        $random = bin2hex(random_bytes(4));
        
        return "{$basename}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Utworzenie katalogów
     */
    private function ensureDirectoriesExist(): void
    {
        $directories = [$this->uploadPath, $this->thumbnailPath];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception("Nie można utworzyć katalogu: {$dir}");
                }
            }
        }
    }

    /**
     * Pobranie komunikatu błędu uploadu
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'Plik jest za duży';
            case UPLOAD_ERR_PARTIAL:
                return 'Plik został przesłany tylko częściowo';
            case UPLOAD_ERR_NO_FILE:
                return 'Nie wybrano pliku';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Brak katalogu tymczasowego';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Błąd zapisu pliku';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload zatrzymany przez rozszerzenie';
            default:
                return 'Nieznany błąd uploadu';
        }
    }

    /**
     * Pobranie jakości dla rozmiaru miniatury
     */
    private function getQualityForSize(string $size): int
    {
        switch ($size) {
            case 'small':
                return 80;
            case 'medium':
                return 85;
            case 'large':
                return 90;
            default:
                return 85;
        }
    }

    /**
     * Usunięcie pliku i miniatur
     */
    public function deleteImage(string $filename): void
    {
        // Usunięcie głównego pliku
        $mainPath = $this->uploadPath . '/' . $filename;
        if (file_exists($mainPath)) {
            unlink($mainPath);
        }
        
        // Usunięcie miniatur
        foreach ($this->thumbnailSizes as $size => $dimensions) {
            $thumbnailPath = $this->thumbnailPath . '/' . $size . '_' . $filename;
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
        }
    }

    /**
     * Pobranie informacji o pliku
     */
    public function getImageInfo(string $filename): ?array
    {
        $filePath = $this->uploadPath . '/' . $filename;
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        $imageInfo = getimagesize($filePath);
        if ($imageInfo === false) {
            return null;
        }
        
        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime_type' => $imageInfo['mime'],
            'file_size' => filesize($filePath),
            'url' => uploadUrl('items/' . $filename)
        ];
    }
}
