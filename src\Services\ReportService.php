<?php

namespace App\Services;

use App\Database\Database;

/**
 * Serwis do generowania raportów
 */
class ReportService
{
    private Database $db;

    public function __construct()
    {
        $this->db = DB;
    }

    /**
     * Raport stanu magazynu
     */
    public function getInventoryReport(array $filters = []): array
    {
        $query = "SELECT 
                    i.id,
                    i.name,
                    i.description,
                    c.name as category_name,
                    i.status,
                    i.is_lendable,
                    CONCAT(u.first_name, ' ', u.last_name) as current_holder,
                    i.created_at,
                    (SELECT COUNT(*) FROM item_images WHERE item_id = i.id) as images_count,
                    (SELECT COUNT(*) FROM loans WHERE item_id = i.id) as total_loans
                  FROM items i
                  LEFT JOIN categories c ON i.category_id = c.id
                  LEFT JOIN users u ON i.current_holder_id = u.id";
        
        $params = [];
        $conditions = [];
        
        if (!empty($filters['category_id'])) {
            $conditions[] = "i.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['status'])) {
            $conditions[] = "i.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $conditions[] = "(i.name LIKE :search OR i.description LIKE :search)";
            $params['search'] = "%{$filters['search']}%";
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY c.name, i.name";
        
        return $this->db->select($query, $params);
    }

    /**
     * Statystyki wypożyczeń
     */
    public function getLoanStats(array $filters = []): array
    {
        $baseQuery = "FROM loans l 
                      JOIN items i ON l.item_id = i.id 
                      JOIN users u ON l.borrower_id = u.id";
        
        $params = [];
        $conditions = [];
        
        if (!empty($filters['status'])) {
            if ($filters['status'] === 'overdue') {
                $conditions[] = "l.status = 'active' AND l.expected_return_date < CURDATE()";
            } else {
                $conditions[] = "l.status = :status";
                $params['status'] = $filters['status'];
            }
        }
        
        if (!empty($filters['user_id'])) {
            $conditions[] = "l.borrower_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $conditions[] = "l.loan_date >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $conditions[] = "l.loan_date <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        $whereClause = !empty($conditions) ? " WHERE " . implode(' AND ', $conditions) : "";
        
        // Podstawowe statystyki
        $stats = $this->db->selectOne("
            SELECT 
                COUNT(*) as total_loans,
                SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as active_loans,
                SUM(CASE WHEN l.status = 'returned' THEN 1 ELSE 0 END) as returned_loans,
                SUM(CASE WHEN l.status = 'active' AND l.expected_return_date < CURDATE() THEN 1 ELSE 0 END) as overdue_loans,
                AVG(CASE WHEN l.status = 'returned' THEN DATEDIFF(l.actual_return_date, l.loan_date) ELSE NULL END) as avg_loan_duration
            {$baseQuery} {$whereClause}
        ", $params);
        
        // Najpopularniejsze przedmioty
        $popularItems = $this->db->select("
            SELECT 
                i.name,
                COUNT(*) as loan_count
            {$baseQuery} {$whereClause}
            GROUP BY i.id, i.name
            ORDER BY loan_count DESC
            LIMIT 10
        ", $params);
        
        return [
            'summary' => $stats,
            'popular_items' => $popularItems
        ];
    }

    /**
     * Statystyki użytkowników
     */
    public function getUserStats(): array
    {
        $query = "SELECT 
                    u.id,
                    CONCAT(u.first_name, ' ', u.last_name) as full_name,
                    u.username,
                    u.role,
                    COUNT(l.id) as total_loans,
                    SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as active_loans,
                    SUM(CASE WHEN l.status = 'active' AND l.expected_return_date < CURDATE() THEN 1 ELSE 0 END) as overdue_loans,
                    MAX(l.loan_date) as last_loan_date
                  FROM users u
                  LEFT JOIN loans l ON u.id = l.borrower_id
                  WHERE u.is_active = 1 AND u.role = 'geodeta'
                  GROUP BY u.id, u.first_name, u.last_name, u.username, u.role
                  ORDER BY total_loans DESC";
        
        return $this->db->select($query);
    }

    /**
     * Najaktywniejszi wypożyczający
     */
    public function getTopBorrowers(int $limit = 10): array
    {
        $query = "SELECT 
                    CONCAT(u.first_name, ' ', u.last_name) as full_name,
                    u.username,
                    COUNT(l.id) as total_loans,
                    SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as active_loans
                  FROM users u
                  JOIN loans l ON u.id = l.borrower_id
                  WHERE u.is_active = 1
                  GROUP BY u.id, u.first_name, u.last_name, u.username
                  ORDER BY total_loans DESC
                  LIMIT :limit";
        
        return $this->db->select($query, ['limit' => $limit]);
    }

    /**
     * Użytkownicy z przeterminowanymi wypożyczeniami
     */
    public function getUsersWithOverdueLoans(): array
    {
        $query = "SELECT 
                    CONCAT(u.first_name, ' ', u.last_name) as full_name,
                    u.username,
                    u.email,
                    COUNT(l.id) as overdue_count,
                    MIN(l.expected_return_date) as oldest_overdue_date,
                    MAX(DATEDIFF(CURDATE(), l.expected_return_date)) as max_days_overdue
                  FROM users u
                  JOIN loans l ON u.id = l.borrower_id
                  WHERE l.status = 'active' AND l.expected_return_date < CURDATE()
                  GROUP BY u.id, u.first_name, u.last_name, u.username, u.email
                  ORDER BY max_days_overdue DESC";
        
        return $this->db->select($query);
    }

    /**
     * Historia przedmiotu
     */
    public function getItemHistory(int $itemId): array
    {
        $query = "SELECT 
                    t.transfer_date,
                    t.transfer_type,
                    t.notes,
                    CASE 
                        WHEN t.from_user_id IS NULL THEN 'Magazyn'
                        ELSE CONCAT(from_user.first_name, ' ', from_user.last_name)
                    END as from_user,
                    CASE 
                        WHEN t.to_user_id IS NULL THEN 'Magazyn'
                        ELSE CONCAT(to_user.first_name, ' ', to_user.last_name)
                    END as to_user,
                    CONCAT(processed_by.first_name, ' ', processed_by.last_name) as processed_by,
                    l.loan_date,
                    l.expected_return_date,
                    l.actual_return_date
                  FROM transfers t
                  LEFT JOIN users from_user ON t.from_user_id = from_user.id
                  LEFT JOIN users to_user ON t.to_user_id = to_user.id
                  LEFT JOIN users processed_by ON t.processed_by_id = processed_by.id
                  LEFT JOIN loans l ON t.loan_id = l.id
                  WHERE t.item_id = :item_id
                  ORDER BY t.transfer_date DESC, t.created_at DESC";
        
        return $this->db->select($query, ['item_id' => $itemId]);
    }

    /**
     * Statystyki przedmiotu
     */
    public function getItemStats(int $itemId): array
    {
        $stats = $this->db->selectOne("
            SELECT 
                COUNT(l.id) as total_loans,
                SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as active_loans,
                SUM(CASE WHEN l.status = 'returned' THEN 1 ELSE 0 END) as returned_loans,
                AVG(CASE WHEN l.status = 'returned' THEN DATEDIFF(l.actual_return_date, l.loan_date) ELSE NULL END) as avg_loan_duration,
                MIN(l.loan_date) as first_loan_date,
                MAX(l.loan_date) as last_loan_date
            FROM loans l
            WHERE l.item_id = :item_id
        ", ['item_id' => $itemId]);
        
        $borrowers = $this->db->select("
            SELECT 
                CONCAT(u.first_name, ' ', u.last_name) as full_name,
                COUNT(l.id) as loan_count,
                MAX(l.loan_date) as last_loan_date
            FROM loans l
            JOIN users u ON l.borrower_id = u.id
            WHERE l.item_id = :item_id
            GROUP BY u.id, u.first_name, u.last_name
            ORDER BY loan_count DESC
            LIMIT 10
        ", ['item_id' => $itemId]);
        
        return [
            'summary' => $stats,
            'top_borrowers' => $borrowers
        ];
    }

    /**
     * Dane wypożyczeń do eksportu
     */
    public function getLoansForExport(array $filters = []): array
    {
        $query = "SELECT 
                    l.id as 'ID',
                    i.name as 'Przedmiot',
                    c.name as 'Kategoria',
                    CONCAT(borrower.first_name, ' ', borrower.last_name) as 'Wypożyczający',
                    CONCAT(lender.first_name, ' ', lender.last_name) as 'Wydający',
                    l.loan_date as 'Data wypożyczenia',
                    l.expected_return_date as 'Planowany zwrot',
                    l.actual_return_date as 'Rzeczywisty zwrot',
                    l.status as 'Status',
                    l.notes as 'Uwagi'
                  FROM loans l
                  JOIN items i ON l.item_id = i.id
                  LEFT JOIN categories c ON i.category_id = c.id
                  JOIN users borrower ON l.borrower_id = borrower.id
                  JOIN users lender ON l.lender_id = lender.id";
        
        $params = [];
        $conditions = [];
        
        // Zastosowanie filtrów (podobnie jak w getLoanStats)
        if (!empty($filters['status'])) {
            $conditions[] = "l.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['user_id'])) {
            $conditions[] = "l.borrower_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($conditions)) {
            $query .= " WHERE " . implode(' AND ', $conditions);
        }
        
        $query .= " ORDER BY l.loan_date DESC";
        
        return $this->db->select($query, $params);
    }

    /**
     * Dane użytkowników do eksportu
     */
    public function getUsersForExport(): array
    {
        $query = "SELECT 
                    u.id as 'ID',
                    CONCAT(u.first_name, ' ', u.last_name) as 'Imię i nazwisko',
                    u.username as 'Nazwa użytkownika',
                    u.email as 'Email',
                    u.role as 'Rola',
                    COUNT(l.id) as 'Liczba wypożyczeń',
                    SUM(CASE WHEN l.status = 'active' THEN 1 ELSE 0 END) as 'Aktywne wypożyczenia',
                    u.created_at as 'Data utworzenia'
                  FROM users u
                  LEFT JOIN loans l ON u.id = l.borrower_id
                  WHERE u.is_active = 1
                  GROUP BY u.id, u.first_name, u.last_name, u.username, u.email, u.role, u.created_at
                  ORDER BY u.last_name, u.first_name";
        
        return $this->db->select($query);
    }

    /**
     * Raport miesięczny
     */
    public function getMonthlyReport(int $year, int $month): array
    {
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));
        
        $summary = $this->db->selectOne("
            SELECT 
                COUNT(DISTINCT l.id) as total_loans,
                COUNT(DISTINCT CASE WHEN l.loan_date BETWEEN :start_date AND :end_date THEN l.id END) as new_loans,
                COUNT(DISTINCT CASE WHEN l.actual_return_date BETWEEN :start_date AND :end_date THEN l.id END) as returned_loans,
                COUNT(DISTINCT l.borrower_id) as unique_borrowers,
                COUNT(DISTINCT l.item_id) as unique_items
            FROM loans l
            WHERE l.loan_date <= :end_date
        ", [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        
        $dailyStats = $this->db->select("
            SELECT 
                DATE(l.loan_date) as date,
                COUNT(*) as loans_count
            FROM loans l
            WHERE l.loan_date BETWEEN :start_date AND :end_date
            GROUP BY DATE(l.loan_date)
            ORDER BY date
        ", [
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);
        
        return [
            'summary' => $summary,
            'daily_stats' => $dailyStats,
            'period' => [
                'start' => $startDate,
                'end' => $endDate,
                'month_name' => date('F Y', strtotime($startDate))
            ]
        ];
    }
}
