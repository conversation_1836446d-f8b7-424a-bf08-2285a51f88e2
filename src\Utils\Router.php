<?php

namespace App\Utils;

use Exception;

/**
 * Prosty router dla aplikacji
 */
class Router
{
    private $routes = [];
    private $middleware = [];
    private $notFoundHandler;
    private $errorHandler;

    /**
     * Dodanie trasy GET
     */
    public function get($path, $handler)
    {
        $this->addRoute('GET', $path, $handler);
    }

    /**
     * Dodanie trasy POST
     */
    public function post($path, $handler)
    {
        $this->addRoute('POST', $path, $handler);
    }

    /**
     * Dodanie trasy PUT
     */
    public function put($path, $handler)
    {
        $this->addRoute('PUT', $path, $handler);
    }

    /**
     * Dodanie trasy DELETE
     */
    public function delete($path, $handler)
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    /**
     * Dodanie trasy dla dowolnej metody
     */
    private function addRoute($method, $path, $handler)
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'pattern' => $this->createPattern($path)
        ];
    }

    /**
     * Utworzenie wzorca regex z ścieżki
     */
    private function createPattern($path)
    {
        // Zamiana parametrów {param} na grupy regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $path);

        // Escape innych znaków regex
        $pattern = str_replace('/', '\/', $pattern);

        return '/^' . $pattern . '$/';
    }

    /**
     * Dodanie middleware
     */
    public function addMiddleware($middleware)
    {
        $this->middleware[] = $middleware;
    }

    /**
     * Ustawienie handlera dla 404
     */
    public function setNotFoundHandler($handler)
    {
        $this->notFoundHandler = $handler;
    }

    /**
     * Ustawienie handlera dla błędów
     */
    public function setErrorHandler($handler)
    {
        $this->errorHandler = $handler;
    }

    /**
     * Uruchomienie routera (alias dla dispatch)
     */
    public function dispatch()
    {
        $this->run();
    }

    /**
     * Uruchomienie routera
     */
    public function run()
    {
        try {
            $method = $_SERVER['REQUEST_METHOD'];
            $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
            
            // Znalezienie pasującej trasy
            $route = $this->findRoute($method, $path);
            
            if (!$route) {
                $this->handleNotFound();
                return;
            }
            
            // Wykonanie middleware
            $this->runMiddleware();
            
            // Wykonanie handlera
            $this->executeHandler($route['handler'], $route['params']);
            
        } catch (Exception $e) {
            $this->handleError($e);
        }
    }

    /**
     * Znalezienie pasującej trasy
     */
    private function findRoute(string $method, string $path): ?array
    {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            if (preg_match($route['pattern'], $path, $matches)) {
                // Usunięcie pełnego dopasowania
                array_shift($matches);
                
                return [
                    'handler' => $route['handler'],
                    'params' => $matches
                ];
            }
        }
        
        return null;
    }

    /**
     * Wykonanie middleware
     */
    private function runMiddleware()
    {
        foreach ($this->middleware as $middleware) {
            $next = function($request, $response) {
                return $response;
            };

            $middleware(null, null, $next);
        }
    }

    /**
     * Wykonanie handlera
     */
    private function executeHandler($handler, $params = [])
    {
        if (is_callable($handler)) {
            // Funkcja anonimowa
            call_user_func_array($handler, $params);
        } elseif (is_string($handler) && strpos($handler, '@') !== false) {
            // Kontroler@metoda
            [$controllerName, $method] = explode('@', $handler);
            
            $controllerClass = "App\\Controllers\\{$controllerName}";
            
            if (!class_exists($controllerClass)) {
                throw new Exception("Kontroler {$controllerClass} nie istnieje");
            }
            
            $controller = new $controllerClass();
            
            if (!method_exists($controller, $method)) {
                throw new Exception("Metoda {$method} nie istnieje w kontrolerze {$controllerClass}");
            }
            
            call_user_func_array([$controller, $method], $params);
        } else {
            throw new Exception('Nieprawidłowy handler trasy');
        }
    }

    /**
     * Obsługa błędu 404
     */
    private function handleNotFound()
    {
        if ($this->notFoundHandler) {
            call_user_func($this->notFoundHandler);
        } else {
            http_response_code(404);
            echo '404 - Nie znaleziono strony';
        }
    }

    /**
     * Obsługa błędów
     */
    private function handleError($e)
    {
        if ($this->errorHandler) {
            call_user_func($this->errorHandler, $e);
        } else {
            http_response_code(500);
            echo '500 - Błąd serwera';
            
            if (CONFIG['app']['debug']) {
                echo '<br><br>' . $e->getMessage();
                echo '<pre>' . $e->getTraceAsString() . '</pre>';
            }
        }
    }
}
