<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= e($pageTitle ?? 'Logowanie') ?> - <?= e(CONFIG['app']['name']) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= asset('css/auth.css') ?>" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid vh-100">
        <div class="row h-100">
            <!-- <PERSON>wa strona - formularz logowania -->
            <div class="col-md-6 d-flex align-items-center justify-content-center">
                <div class="login-form-container">
                    <div class="text-center mb-4">
                        <i class="bi bi-box-seam text-primary" style="font-size: 3rem;"></i>
                        <h1 class="h3 mb-3 fw-normal"><?= e(CONFIG['app']['name']) ?></h1>
                        <p class="text-muted">System zarządzania magazynem</p>
                    </div>

                    <!-- Komunikaty flash -->
                    <?php foreach (getFlashMessages() as $message): ?>
                        <div class="alert alert-<?= $message['type'] === 'error' ? 'danger' : $message['type'] ?> alert-dismissible fade show" role="alert">
                            <?= e($message['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endforeach; ?>

                    <form id="loginForm" method="POST" action="/login" class="needs-validation" novalidate>
                        <?= csrfField() ?>
                        
                        <div class="form-floating mb-3">
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   placeholder="Nazwa użytkownika"
                                   value="<?= e($_POST['username'] ?? '') ?>"
                                   required
                                   autocomplete="username">
                            <label for="username">
                                <i class="bi bi-person me-2"></i>Nazwa użytkownika
                            </label>
                            <div class="invalid-feedback">
                                Proszę podać nazwę użytkownika.
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            <input type="password" 
                                   class="form-control" 
                                   id="password" 
                                   name="password" 
                                   placeholder="Hasło"
                                   required
                                   autocomplete="current-password">
                            <label for="password">
                                <i class="bi bi-lock me-2"></i>Hasło
                            </label>
                            <div class="invalid-feedback">
                                Proszę podać hasło.
                            </div>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="remember_me" 
                                   name="remember_me"
                                   <?= isset($_POST['remember_me']) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="remember_me">
                                Zapamiętaj mnie
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 py-2" id="loginBtn">
                            <span class="btn-text">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Zaloguj się
                            </span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                Logowanie...
                            </span>
                        </button>
                    </form>

                    <div class="text-center mt-4">
                        <small class="text-muted">
                            Problemy z logowaniem? Skontaktuj się z administratorem.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Prawa strona - informacje o systemie -->
            <div class="col-md-6 bg-primary text-white d-flex align-items-center justify-content-center">
                <div class="text-center">
                    <i class="bi bi-geo-alt display-1 mb-4"></i>
                    <h2 class="mb-4">System Magazynowy</h2>
                    <p class="lead mb-4">
                        Nowoczesne zarządzanie wypożyczaniem sprzętu geodezyjnego, komputerowego i biurowego.
                    </p>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <i class="bi bi-tools display-6"></i>
                            <p class="mt-2">Sprzęt geodezyjny</p>
                        </div>
                        <div class="col-4">
                            <i class="bi bi-laptop display-6"></i>
                            <p class="mt-2">Sprzęt komputerowy</p>
                        </div>
                        <div class="col-4">
                            <i class="bi bi-printer display-6"></i>
                            <p class="mt-2">Sprzęt biurowy</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <script>
        // Walidacja formularza
        (function() {
            'use strict';
            
            const form = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');
            
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    // Pokazanie loadingu
                    loginBtn.disabled = true;
                    btnText.classList.add('d-none');
                    btnLoading.classList.remove('d-none');
                }
                
                form.classList.add('was-validated');
            });
            
            // Focus na pierwszym polu
            document.getElementById('username').focus();
            
            // Ukrycie alertów po 5 sekundach
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        })();
    </script>
</body>
</html>
