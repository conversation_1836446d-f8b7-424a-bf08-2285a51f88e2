<?php
$content = ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-person-workspace me-2"></i>Panel Geodety
            </h1>
            <div class="text-muted">
                <i class="bi bi-clock me-1"></i>
                <?= formatDateTime(date('Y-m-d H:i:s')) ?>
            </div>
        </div>
    </div>
</div>

<!-- Statystyki dla geodety -->
<div class="row mb-4">
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Moje przedmioty
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['my_items'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-box text-primary" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Moje rezerwacje
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['my_reservations'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-calendar-check text-success" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Aktywne wypożyczenia
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['my_loans'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-arrow-right-circle text-warning" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alerty i powiadomienia -->
<?php if (!empty($alerts)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-exclamation-triangle me-2"></i>Powiadomienia
                </h6>
            </div>
            <div class="card-body">
                <?php foreach ($alerts as $alert): ?>
                    <div class="alert alert-<?= $alert['type'] ?> d-flex align-items-center" role="alert">
                        <i class="bi bi-<?= $alert['icon'] ?> me-3"></i>
                        <div class="flex-grow-1">
                            <strong><?= e($alert['title']) ?></strong><br>
                            <small><?= e($alert['message']) ?></small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Ostatnie aktywności -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history me-2"></i>Ostatnie aktywności
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentActivities)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-inbox display-6"></i>
                        <p class="mt-2 mb-0 small">Brak ostatnich aktywności</p>
                    </div>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($recentActivities as $activity): ?>
                            <div class="timeline-item mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <?php if ($activity['type'] === 'loan'): ?>
                                            <i class="bi bi-arrow-right-circle text-primary"></i>
                                        <?php else: ?>
                                            <i class="bi bi-calendar-check text-success"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small">
                                            <strong><?= e($activity['description']) ?></strong>
                                        </div>
                                        <div class="text-muted small">
                                            <?= formatDateTime($activity['created_at']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Szybkie akcje -->
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning me-2"></i>Szybkie akcje
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <a href="/items" class="btn btn-primary w-100">
                            <i class="bi bi-search me-2"></i>Przeglądaj przedmioty
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="/loans" class="btn btn-success w-100">
                            <i class="bi bi-arrow-right-circle me-2"></i>Moje wypożyczenia
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="/reservations" class="btn btn-info w-100">
                            <i class="bi bi-calendar-check me-2"></i>Moje rezerwacje
                        </a>
                    </div>
                    <div class="col-12 mb-3">
                        <a href="/transfers" class="btn btn-warning w-100">
                            <i class="bi bi-arrow-left-right me-2"></i>Prośby o przekazanie
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include TEMPLATES_PATH . '/layout/main.php';
?>
