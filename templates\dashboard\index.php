<?php
$content = ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-speedometer2 me-2"></i>Dashboard
            </h1>
            <div class="text-muted">
                <i class="bi bi-clock me-1"></i>
                <?= formatDateTime(date('Y-m-d H:i:s')) ?>
            </div>
        </div>
    </div>
</div>

<!-- Statystyki -->
<div class="row mb-4">
    <?php if ($isAdmin): ?>
        <!-- Statystyki dla administratora -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Wszystkie przedmioty
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['total_items']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-box text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Dostępne
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['available_items']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Wypożyczone
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['lent_items']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-right-circle text-warning" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Przeterminowane
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['overdue_loans']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Statystyki dla geodety -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Moje wypożyczenia
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['my_active_loans']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-right-circle text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Dostępne przedmioty
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['available_items']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Moje prośby
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['my_transfer_requests']) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-arrow-left-right text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<div class="row">
    <!-- Alerty i powiadomienia -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-bell me-2"></i>Alerty i powiadomienia
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($alerts)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-check-circle display-4 text-success"></i>
                        <p class="mt-3 mb-0">Brak alertów - wszystko w porządku!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($alerts as $alert): ?>
                        <div class="alert alert-<?= $alert['type'] ?> d-flex align-items-center" role="alert">
                            <i class="bi bi-<?= $alert['icon'] ?> me-3"></i>
                            <div class="flex-grow-1">
                                <strong><?= e($alert['title']) ?></strong><br>
                                <small><?= e($alert['message']) ?></small>
                            </div>
                            <?php if (!empty($alert['link'])): ?>
                                <a href="<?= e($alert['link']) ?>" class="btn btn-sm btn-outline-<?= $alert['type'] ?>">
                                    Zobacz
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Ostatnie aktywności -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history me-2"></i>Ostatnie aktywności
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($recentActivities)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-inbox display-6"></i>
                        <p class="mt-2 mb-0 small">Brak ostatnich aktywności</p>
                    </div>
                <?php else: ?>
                    <div class="timeline">
                        <?php foreach ($recentActivities as $activity): ?>
                            <div class="timeline-item mb-3">
                                <div class="d-flex">
                                    <div class="flex-shrink-0">
                                        <?php if ($activity['type'] === 'loan'): ?>
                                            <i class="bi bi-arrow-right-circle text-primary"></i>
                                        <?php else: ?>
                                            <i class="bi bi-calendar-check text-success"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="small">
                                            <strong><?= e($activity['user_name']) ?></strong>
                                            <?= $activity['type'] === 'loan' ? 'wypożyczył' : 'zarezerwował' ?>
                                            <strong><?= e($activity['item_name']) ?></strong>
                                        </div>
                                        <div class="text-muted small">
                                            <?= formatDate($activity['date']) ?>
                                            <span class="badge bg-<?= $activity['status'] === 'active' ? 'success' : 'secondary' ?> ms-2">
                                                <?= e($activity['status']) ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Szybkie akcje -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning me-2"></i>Szybkie akcje
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if ($isAdmin): ?>
                        <div class="col-md-3 mb-3">
                            <a href="/items/create" class="btn btn-primary w-100">
                                <i class="bi bi-plus-circle me-2"></i>Dodaj przedmiot
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/loans/create" class="btn btn-success w-100">
                                <i class="bi bi-arrow-right-circle me-2"></i>Nowe wypożyczenie
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/reservations?status=pending" class="btn btn-warning w-100">
                                <i class="bi bi-calendar-check me-2"></i>Oczekujące rezerwacje
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/reports/inventory" class="btn btn-info w-100">
                                <i class="bi bi-graph-up me-2"></i>Raport magazynu
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="col-md-4 mb-3">
                            <a href="/items" class="btn btn-primary w-100">
                                <i class="bi bi-search me-2"></i>Przeglądaj przedmioty
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="/loans" class="btn btn-success w-100">
                                <i class="bi bi-arrow-right-circle me-2"></i>Moje wypożyczenia
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="/reservations" class="btn btn-info w-100">
                                <i class="bi bi-calendar-check me-2"></i>Moje rezerwacje
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include TEMPLATES_PATH . '/layout/main.php';
?>
