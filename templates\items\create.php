<?php
/**
 * Formularz dodawania przedmiotu
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

// Sprawdzenie uprawnień
if (!$authController->hasPermission('manage_items')) {
    flash('error', 'Brak uprawnień');
    redirect('/items');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-plus-circle text-primary me-2"></i>
                        Dodaj przedmiot
                    </h1>
                    <p class="text-muted mb-0">Dodaj nowy przedmiot do magazynu</p>
                </div>
                <a href="/items" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Powrót do listy
                </a>
            </div>

            <!-- Formularz -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                Informacje o przedmiocie
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="/items" method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                                <?= csrfField() ?>
                                <div class="row">
                                    <!-- Nazwa -->
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">
                                            Nazwa przedmiotu <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="name" 
                                               name="name" 
                                               required 
                                               maxlength="100"
                                               value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                                        <div class="invalid-feedback">
                                            Nazwa przedmiotu jest wymagana
                                        </div>
                                    </div>

                                    <!-- Kategoria -->
                                    <div class="col-md-6 mb-3">
                                        <label for="category_id" class="form-label">
                                            Kategoria <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Wybierz kategorię</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= $category['id'] ?>" 
                                                        <?= (($_POST['category_id'] ?? '') == $category['id']) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">
                                            Wybierz kategorię
                                        </div>
                                    </div>
                                </div>

                                <!-- Opis -->
                                <div class="mb-3">
                                    <label for="description" class="form-label">Opis</label>
                                    <textarea class="form-control" 
                                              id="description" 
                                              name="description" 
                                              rows="3" 
                                              maxlength="500"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                                    <div class="form-text">Krótki opis przedmiotu (maksymalnie 500 znaków)</div>
                                </div>

                                <!-- Szczegóły -->
                                <div class="mb-3">
                                    <label for="details" class="form-label">Szczegóły techniczne</label>
                                    <textarea class="form-control" 
                                              id="details" 
                                              name="details" 
                                              rows="4"><?= htmlspecialchars($_POST['details'] ?? '') ?></textarea>
                                    <div class="form-text">Szczegółowe informacje techniczne, specyfikacja, itp.</div>
                                </div>

                                <div class="row">
                                    <!-- Status -->
                                    <div class="col-md-6 mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="available" <?= (($_POST['status'] ?? 'available') == 'available') ? 'selected' : '' ?>>
                                                Dostępny
                                            </option>
                                            <option value="maintenance" <?= (($_POST['status'] ?? '') == 'maintenance') ? 'selected' : '' ?>>
                                                W serwisie
                                            </option>
                                            <option value="damaged" <?= (($_POST['status'] ?? '') == 'damaged') ? 'selected' : '' ?>>
                                                Uszkodzony
                                            </option>
                                            <option value="retired" <?= (($_POST['status'] ?? '') == 'retired') ? 'selected' : '' ?>>
                                                Wycofany
                                            </option>
                                        </select>
                                    </div>

                                    <!-- Czy można wypożyczyć -->
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="is_lendable" 
                                                   name="is_lendable" 
                                                   value="1"
                                                   <?= isset($_POST['is_lendable']) ? 'checked' : 'checked' ?>>
                                            <label class="form-check-label" for="is_lendable">
                                                <i class="bi bi-arrow-repeat me-1"></i>
                                                Można wypożyczyć
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Zdjęcia -->
                                <div class="mb-4">
                                    <label for="images" class="form-label">
                                        <i class="bi bi-camera me-1"></i>
                                        Zdjęcia przedmiotu
                                    </label>
                                    <input type="file" 
                                           class="form-control" 
                                           id="images" 
                                           name="images[]" 
                                           multiple 
                                           accept="image/*">
                                    <div class="form-text">
                                        Możesz wybrać wiele zdjęć. Dozwolone formaty: JPG, PNG, GIF (maksymalnie 5MB każde)
                                    </div>
                                </div>

                                <!-- Przyciski -->
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-2"></i>
                                        Dodaj przedmiot
                                    </button>
                                    <a href="/items" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-lg me-2"></i>
                                        Anuluj
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar z pomocą -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-lightbulb me-2"></i>
                                Wskazówki
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Podaj dokładną nazwę przedmiotu
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Wybierz odpowiednią kategorię
                                </li>
                                <li class="mb-2">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Dodaj zdjęcia dla lepszej identyfikacji
                                </li>
                                <li class="mb-0">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    Wypełnij szczegóły techniczne
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Walidacja formularza
(function() {
    'use strict';
    
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    });
    
    // Podgląd zdjęć
    const imageInput = document.getElementById('images');
    imageInput.addEventListener('change', function(e) {
        const files = e.target.files;
        if (files.length > 0) {
            console.log(`Wybrano ${files.length} plików`);
        }
    });
})();
</script>
