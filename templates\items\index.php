<?php
$content = ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-box me-2"></i>Przedmioty
            </h1>
            <?php if (hasPermission('manage_items')): ?>
                <a href="/items/create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Dodaj przedmiot
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Filtry -->
<div class="search-filters mb-4">
    <form method="GET" action="/items" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">Wyszukaj</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="<?= e($_GET['search'] ?? '') ?>" 
                   placeholder="Nazwa lub opis przedmiotu">
        </div>
        
        <div class="col-md-3">
            <label for="category" class="form-label">Kategoria</label>
            <select class="form-select" id="category" name="category">
                <option value="">Wszystkie kategorie</option>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= $category['id'] ?>" 
                            <?= ($_GET['category'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                        <?= e($category['name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="col-md-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="">Wszystkie statusy</option>
                <option value="available" <?= ($_GET['status'] ?? '') === 'available' ? 'selected' : '' ?>>Dostępny</option>
                <option value="reserved" <?= ($_GET['status'] ?? '') === 'reserved' ? 'selected' : '' ?>>Zarezerwowany</option>
                <option value="lent" <?= ($_GET['status'] ?? '') === 'lent' ? 'selected' : '' ?>>Wypożyczony</option>
                <option value="maintenance" <?= ($_GET['status'] ?? '') === 'maintenance' ? 'selected' : '' ?>>W serwisie</option>
            </select>
        </div>
        
        <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-outline-primary me-2">
                <i class="bi bi-search"></i> Szukaj
            </button>
            <a href="/items" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i>
            </a>
        </div>
    </form>
</div>

<!-- Lista przedmiotów -->
<div class="row">
    <?php if (empty($items)): ?>
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">Brak przedmiotów</h4>
                <p class="text-muted">Nie znaleziono przedmiotów spełniających kryteria wyszukiwania.</p>
                <?php if (hasPermission('manage_items')): ?>
                    <a href="/items/create" class="btn btn-primary mt-3">
                        <i class="bi bi-plus-circle me-2"></i>Dodaj pierwszy przedmiot
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($items as $item): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100">
                    <?php if ($item['primary_image']): ?>
                        <img src="<?= uploadUrl('thumbnails/medium_' . $item['primary_image']) ?>" 
                             class="card-img-top" 
                             alt="<?= e($item['name']) ?>"
                             style="height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?= e($item['name']) ?></h5>
                        <p class="card-text text-muted small mb-2">
                            <i class="bi bi-tag me-1"></i><?= e($item['category_name']) ?>
                        </p>
                        
                        <?php if ($item['description']): ?>
                            <p class="card-text"><?= e(substr($item['description'], 0, 100)) ?><?= strlen($item['description']) > 100 ? '...' : '' ?></p>
                        <?php endif; ?>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span class="badge bg-<?= getStatusColor($item['status']) ?>">
                                    <?= getStatusName($item['status']) ?>
                                </span>
                                
                                <?php if ($item['current_holder_name']): ?>
                                    <small class="text-muted">
                                        <i class="bi bi-person me-1"></i><?= e($item['current_holder_name']) ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            
                            <div class="btn-group w-100" role="group">
                                <a href="/items/<?= $item['id'] ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>Zobacz
                                </a>
                                
                                <?php if ($item['status'] === 'available' && $item['is_lendable']): ?>
                                    <?php if (hasPermission('approve_loans')): ?>
                                        <button type="button" class="btn btn-success btn-sm" 
                                                onclick="showLoanModal(<?= $item['id'] ?>)">
                                            <i class="bi bi-arrow-right-circle me-1"></i>Wydaj
                                        </button>
                                    <?php else: ?>
                                        <button type="button" class="btn btn-warning btn-sm" 
                                                onclick="showReservationModal(<?= $item['id'] ?>)">
                                            <i class="bi bi-calendar-check me-1"></i>Zarezerwuj
                                        </button>
                                    <?php endif; ?>
                                <?php elseif ($item['status'] === 'lent' && !hasRole('admin') && $item['current_holder_name'] !== currentUser()['full_name']): ?>
                                    <button type="button" class="btn btn-info btn-sm" 
                                            onclick="showTransferModal(<?= $item['id'] ?>)">
                                        <i class="bi bi-arrow-left-right me-1"></i>Poproś o transfer
                                    </button>
                                <?php endif; ?>
                                
                                <?php if (hasPermission('manage_items')): ?>
                                    <a href="/items/<?= $item['id'] ?>/edit" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<!-- Paginacja -->
<?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
    <nav aria-label="Paginacja przedmiotów">
        <ul class="pagination justify-content-center">
            <?php if ($pagination['current_page'] > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['prev_url'] ?>">
                        <i class="bi bi-chevron-left"></i> Poprzednia
                    </a>
                </li>
            <?php endif; ?>
            
            <?php for ($i = $pagination['start_page']; $i <= $pagination['end_page']; $i++): ?>
                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                    <a class="page-link" href="<?= str_replace('page=' . $pagination['current_page'], 'page=' . $i, $_SERVER['REQUEST_URI']) ?>">
                        <?= $i ?>
                    </a>
                </li>
            <?php endfor; ?>
            
            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['next_url'] ?>">
                        Następna <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>

<!-- Modal rezerwacji -->
<div class="modal fade" id="reservationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rezerwacja przedmiotu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="reservationForm" class="ajax-form" action="/reservations" method="POST">
                <?= csrfField() ?>
                <input type="hidden" id="reservation_item_id" name="item_id">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="requested_date" class="form-label">Data potrzeby</label>
                        <input type="date" class="form-control" id="requested_date" name="requested_date" 
                               min="<?= date('Y-m-d') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reservation_notes" class="form-label">Uwagi</label>
                        <textarea class="form-control" id="reservation_notes" name="notes" rows="3" 
                                  placeholder="Opcjonalne uwagi do rezerwacji"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-calendar-check me-2"></i>Zarezerwuj
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showReservationModal(itemId) {
    document.getElementById('reservation_item_id').value = itemId;
    document.getElementById('requested_date').value = '';
    document.getElementById('reservation_notes').value = '';
    new bootstrap.Modal(document.getElementById('reservationModal')).show();
}

function showLoanModal(itemId) {
    window.location.href = '/loans/create?item_id=' + itemId;
}

function showTransferModal(itemId) {
    // Implementacja modala transferu
    alert('Funkcja transferu będzie dostępna wkrótce');
}
</script>

<?php
$content = ob_get_clean();
include TEMPLATES_PATH . '/layout/main.php';
?>
