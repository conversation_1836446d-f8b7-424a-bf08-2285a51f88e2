<?php
/**
 * Szczegóły przedmiotu
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-box text-primary me-2"></i>
                        <?= htmlspecialchars($item['name']) ?>
                    </h1>
                    <p class="text-muted mb-0">Szczegóły przedmiotu</p>
                </div>
                <div class="btn-group">
                    <a href="/items" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Powrót do listy
                    </a>
                    <?php if ($authController->hasPermission('manage_items')): ?>
                        <a href="/items/<?= $item['id'] ?>/edit" class="btn btn-primary">
                            <i class="bi bi-pencil me-2"></i>Edytuj
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row">
                <!-- Główne informacje -->
                <div class="col-lg-8">
                    <!-- Zdjęcia -->
                    <?php if (!empty($images)): ?>
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-images me-2"></i>
                                    Zdjęcia
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <?php foreach ($images as $image): ?>
                                        <div class="col-md-4">
                                            <div class="position-relative">
                                                <img src="<?= uploadUrl('items/' . $image['filename']) ?>" 
                                                     class="img-fluid rounded shadow-sm" 
                                                     alt="<?= htmlspecialchars($image['original_name']) ?>"
                                                     style="cursor: pointer;"
                                                     onclick="showImageModal('<?= uploadUrl('items/' . $image['filename']) ?>', '<?= htmlspecialchars($image['original_name']) ?>')">
                                                
                                                <?php if ($image['is_primary']): ?>
                                                    <span class="position-absolute top-0 start-0 badge bg-primary m-2">
                                                        <i class="bi bi-star-fill me-1"></i>Główne
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <?php if ($authController->hasPermission('manage_items')): ?>
                                                    <div class="position-absolute top-0 end-0 m-2">
                                                        <button type="button" 
                                                                class="btn btn-sm btn-danger" 
                                                                onclick="deleteImage(<?= $image['id'] ?>, '<?= htmlspecialchars($image['original_name']) ?>')"
                                                                title="Usuń zdjęcie">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                <?= htmlspecialchars($image['original_name']) ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Informacje podstawowe -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                Informacje podstawowe
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">Nazwa:</label>
                                    <p class="mb-0"><?= htmlspecialchars($item['name']) ?></p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">Kategoria:</label>
                                    <p class="mb-0">
                                        <span class="badge bg-secondary">
                                            <?= htmlspecialchars($item['category_name'] ?? 'Brak kategorii') ?>
                                        </span>
                                    </p>
                                </div>
                            </div>

                            <?php if (!empty($item['description'])): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Opis:</label>
                                    <p class="mb-0"><?= nl2br(htmlspecialchars($item['description'])) ?></p>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($item['details'])): ?>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">Szczegóły techniczne:</label>
                                    <p class="mb-0"><?= nl2br(htmlspecialchars($item['details'])) ?></p>
                                </div>
                            <?php endif; ?>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">Status:</label>
                                    <p class="mb-0">
                                        <?php
                                        $statusClass = match($item['status']) {
                                            'available' => 'bg-success',
                                            'maintenance' => 'bg-warning text-dark',
                                            'damaged' => 'bg-danger',
                                            'retired' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                        
                                        $statusText = match($item['status']) {
                                            'available' => 'Dostępny',
                                            'maintenance' => 'W serwisie',
                                            'damaged' => 'Uszkodzony',
                                            'retired' => 'Wycofany',
                                            default => 'Nieznany'
                                        };
                                        ?>
                                        <span class="badge <?= $statusClass ?>">
                                            <?= $statusText ?>
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-semibold">Możliwość wypożyczenia:</label>
                                    <p class="mb-0">
                                        <?php if ($item['is_lendable']): ?>
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>Tak
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="bi bi-x-circle me-1"></i>Nie
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Akcje -->
                    <?php if ($item['status'] === 'available' && $item['is_lendable']): ?>
                        <div class="card shadow-sm">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="bi bi-arrow-repeat me-2"></i>
                                    Akcje
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex gap-2">
                                    <button type="button" 
                                            class="btn btn-primary" 
                                            onclick="showReservationModal(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')">
                                        <i class="bi bi-calendar-plus me-2"></i>
                                        Zarezerwuj
                                    </button>
                                    
                                    <?php if ($authController->hasPermission('manage_loans')): ?>
                                        <button type="button" 
                                                class="btn btn-success" 
                                                onclick="showLoanModal(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')">
                                            <i class="bi bi-box-arrow-right me-2"></i>
                                            Wypożycz
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Informacje systemowe -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-gear me-2"></i>
                                Informacje systemowe
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <strong>ID:</strong><br>
                                    <small class="text-muted">#<?= $item['id'] ?></small>
                                </li>
                                <li class="mb-2">
                                    <strong>Data dodania:</strong><br>
                                    <small class="text-muted">
                                        <?= date('d.m.Y H:i', strtotime($item['created_at'])) ?>
                                    </small>
                                </li>
                                <?php if ($item['updated_at']): ?>
                                    <li class="mb-2">
                                        <strong>Ostatnia aktualizacja:</strong><br>
                                        <small class="text-muted">
                                            <?= date('d.m.Y H:i', strtotime($item['updated_at'])) ?>
                                        </small>
                                    </li>
                                <?php endif; ?>
                                <li class="mb-0">
                                    <strong>Liczba zdjęć:</strong><br>
                                    <small class="text-muted"><?= count($images ?? []) ?></small>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Historia (dla administratorów) -->
                    <?php if ($authController->hasRole('admin') && !empty($history)): ?>
                        <div class="card shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="bi bi-clock-history me-2"></i>
                                    Historia
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <?php foreach ($history as $entry): ?>
                                        <div class="timeline-item mb-3">
                                            <small class="text-muted">
                                                <?= date('d.m.Y H:i', strtotime($entry['created_at'])) ?>
                                            </small>
                                            <p class="mb-0"><?= htmlspecialchars($entry['description']) ?></p>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal podglądu zdjęcia -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">Podgląd zdjęcia</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="imageModalImg" src="" class="img-fluid" alt="">
            </div>
        </div>
    </div>
</div>

<script>
function showImageModal(imageUrl, imageName) {
    document.getElementById('imageModalImg').src = imageUrl;
    document.getElementById('imageModalTitle').textContent = imageName;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

function deleteImage(imageId, imageName) {
    if (confirm(`Czy na pewno chcesz usunąć zdjęcie "${imageName}"?\n\nTa operacja jest nieodwracalna.`)) {
        fetch(`/images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Błąd: ' + (data.error || 'Nie udało się usunąć zdjęcia'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas usuwania zdjęcia');
        });
    }
}

function showReservationModal(itemId, itemName) {
    // TODO: Implementacja modala rezerwacji
    alert(`Rezerwacja przedmiotu: ${itemName}\n\nFunkcja w przygotowaniu.`);
}

function showLoanModal(itemId, itemName) {
    // TODO: Implementacja modala wypożyczenia
    alert(`Wypożyczenie przedmiotu: ${itemName}\n\nFunkcja w przygotowaniu.`);
}
</script>

<style>
.timeline-item {
    border-left: 2px solid #dee2e6;
    padding-left: 15px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 5px;
    width: 8px;
    height: 8px;
    background: #6c757d;
    border-radius: 50%;
}
</style>
