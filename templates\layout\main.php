<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= e($pageTitle ?? 'Dashboard') ?> - <?= e(CONFIG['app']['name']) ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= asset('css/main.css') ?>" rel="stylesheet">
    
    <?php if (isset($additionalCss)): ?>
        <?php foreach ($additionalCss as $css): ?>
            <link href="<?= asset($css) ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-box-seam me-2"></i>
                <?= e(CONFIG['app']['name']) ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= $_SERVER['REQUEST_URI'] === '/dashboard' ? 'active' : '' ?>" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/items') === 0 ? 'active' : '' ?>" href="/items">
                            <i class="bi bi-box me-1"></i>Przedmioty
                        </a>
                    </li>
                    
                    <?php if (hasPermission('manage_items')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-gear me-1"></i>Zarządzanie
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/items/create">
                                <i class="bi bi-plus-circle me-2"></i>Dodaj przedmiot
                            </a></li>
                            <li><a class="dropdown-item" href="/categories">
                                <i class="bi bi-tags me-2"></i>Kategorie
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/loans">
                                <i class="bi bi-arrow-right-circle me-2"></i>Wypożyczenia
                            </a></li>
                            <li><a class="dropdown-item" href="/reservations">
                                <i class="bi bi-calendar-check me-2"></i>Rezerwacje
                            </a></li>
                            <li><a class="dropdown-item" href="/transfers">
                                <i class="bi bi-arrow-left-right me-2"></i>Transfery
                            </a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/loans') === 0 ? 'active' : '' ?>" href="/loans">
                            <i class="bi bi-arrow-right-circle me-1"></i>Moje wypożyczenia
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/reservations') === 0 ? 'active' : '' ?>" href="/reservations">
                            <i class="bi bi-calendar-check me-1"></i>Moje rezerwacje
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/reports') === 0 ? 'active' : '' ?>" href="/reports">
                            <i class="bi bi-graph-up me-1"></i>Raporty
                        </a>
                    </li>
                    
                    <?php if (hasPermission('manage_users')): ?>
                    <li class="nav-item">
                        <a class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/users') === 0 ? 'active' : '' ?>" href="/users">
                            <i class="bi bi-people me-1"></i>Użytkownicy
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <!-- User menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <?= e(currentUser()['full_name'] ?? 'Użytkownik') ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>Profil
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout">
                                <i class="bi bi-box-arrow-right me-2"></i>Wyloguj
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main content -->
    <main class="container-fluid py-4">
        <!-- Flash messages -->
        <?php foreach (getFlashMessages() as $message): ?>
            <div class="alert alert-<?= $message['type'] === 'error' ? 'danger' : $message['type'] ?> alert-dismissible fade show" role="alert">
                <?= e($message['message']) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endforeach; ?>

        <!-- Page content -->
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <small>
                &copy; <?= date('Y') ?> <?= e(CONFIG['app']['name']) ?> - System zarządzania magazynem
                <?php if (CONFIG['app']['debug']): ?>
                    <span class="badge bg-warning text-dark ms-2">DEBUG MODE</span>
                <?php endif; ?>
            </small>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= asset('js/main.js') ?>"></script>
    
    <?php if (isset($additionalJs)): ?>
        <?php foreach ($additionalJs as $js): ?>
            <script src="<?= asset($js) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Global JavaScript variables -->
    <script>
        window.APP_CONFIG = {
            baseUrl: '<?= url() ?>',
            csrfToken: '<?= csrfToken() ?>',
            user: <?= json_encode(currentUser()) ?>,
            permissions: <?= json_encode(CONFIG['roles'][currentUser()['role'] ?? 'geodeta']['permissions'] ?? []) ?>
        };
    </script>

    <!-- Additional scripts -->
    <?php if (isset($additionalScripts)): ?>
        <?= $additionalScripts ?>
    <?php endif; ?>
</body>
</html>
