<?php
$content = ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-arrow-right-circle me-2"></i><?= e($pageTitle) ?>
            </h1>
            <?php if (hasPermission('approve_loans')): ?>
                <a href="/loans/create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>Nowe wypożyczenie
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Filtry -->
<div class="search-filters mb-4">
    <form method="GET" action="/loans" class="row g-3">
        <div class="col-md-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="">Wszystkie statusy</option>
                <option value="active" <?= ($_GET['status'] ?? '') === 'active' ? 'selected' : '' ?>>Aktywne</option>
                <option value="returned" <?= ($_GET['status'] ?? '') === 'returned' ? 'selected' : '' ?>>Zwrócone</option>
                <option value="overdue" <?= ($_GET['status'] ?? '') === 'overdue' ? 'selected' : '' ?>>Przeterminowane</option>
            </select>
        </div>
        
        <div class="col-md-3">
            <label for="date_from" class="form-label">Data od</label>
            <input type="date" class="form-control" id="date_from" name="date_from" 
                   value="<?= e($_GET['date_from'] ?? '') ?>">
        </div>
        
        <div class="col-md-3">
            <label for="date_to" class="form-label">Data do</label>
            <input type="date" class="form-control" id="date_to" name="date_to" 
                   value="<?= e($_GET['date_to'] ?? '') ?>">
        </div>
        
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-outline-primary me-2">
                <i class="bi bi-search"></i> Filtruj
            </button>
            <a href="/loans" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i>
            </a>
        </div>
    </form>
</div>

<!-- Lista wypożyczeń -->
<?php if (empty($loans)): ?>
    <div class="text-center py-5">
        <i class="bi bi-inbox display-1 text-muted"></i>
        <h4 class="mt-3 text-muted">Brak wypożyczeń</h4>
        <p class="text-muted">Nie znaleziono wypożyczeń spełniających kryteria.</p>
        <?php if (hasPermission('approve_loans')): ?>
            <a href="/loans/create" class="btn btn-primary mt-3">
                <i class="bi bi-plus-circle me-2"></i>Utwórz pierwsze wypożyczenie
            </a>
        <?php endif; ?>
    </div>
<?php else: ?>
    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Przedmiot</th>
                        <th>Wypożyczający</th>
                        <th>Data wypożyczenia</th>
                        <th>Planowany zwrot</th>
                        <th>Status</th>
                        <th>Akcje</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($loans as $loan): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?= e($loan['item_name']) ?></strong>
                                    <?php if ($loan['category_name']): ?>
                                        <br><small class="text-muted"><?= e($loan['category_name']) ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <?= e($loan['borrower_name']) ?>
                                    <br><small class="text-muted">@<?= e($loan['borrower_username']) ?></small>
                                </div>
                            </td>
                            <td>
                                <?= formatDate($loan['loan_date']) ?>
                                <?php if ($loan['lender_name']): ?>
                                    <br><small class="text-muted">przez <?= e($loan['lender_name']) ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($loan['expected_return_date']): ?>
                                    <?= formatDate($loan['expected_return_date']) ?>
                                    <?php if ($loan['computed_status'] === 'overdue'): ?>
                                        <br><small class="text-danger">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            Przeterminowane o <?= $loan['days_overdue'] ?> dni
                                        </small>
                                    <?php elseif ($loan['status'] === 'active'): ?>
                                        <?php 
                                        $daysLeft = (strtotime($loan['expected_return_date']) - time()) / (60 * 60 * 24);
                                        if ($daysLeft <= 3 && $daysLeft >= 0): 
                                        ?>
                                            <br><small class="text-warning">
                                                <i class="bi bi-clock me-1"></i>
                                                Zostało <?= ceil($daysLeft) ?> dni
                                            </small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">Nie określono</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?= getStatusColor($loan['computed_status']) ?>">
                                    <?= getStatusName($loan['computed_status']) ?>
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="/loans/<?= $loan['id'] ?>" class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" title="Zobacz szczegóły">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    
                                    <?php if ($loan['status'] === 'active' && hasPermission('approve_loans')): ?>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="showReturnModal(<?= $loan['id'] ?>)"
                                                data-bs-toggle="tooltip" title="Zwróć przedmiot">
                                            <i class="bi bi-arrow-left-circle"></i>
                                        </button>
                                        
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="showExtendModal(<?= $loan['id'] ?>)"
                                                data-bs-toggle="tooltip" title="Przedłuż wypożyczenie">
                                            <i class="bi bi-calendar-plus"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php endif; ?>

<!-- Paginacja -->
<?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
    <nav aria-label="Paginacja wypożyczeń" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($pagination['current_page'] > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['prev_url'] ?>">
                        <i class="bi bi-chevron-left"></i> Poprzednia
                    </a>
                </li>
            <?php endif; ?>
            
            <?php for ($i = $pagination['start_page']; $i <= $pagination['end_page']; $i++): ?>
                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                    <a class="page-link" href="<?= str_replace('page=' . $pagination['current_page'], 'page=' . $i, $_SERVER['REQUEST_URI']) ?>">
                        <?= $i ?>
                    </a>
                </li>
            <?php endfor; ?>
            
            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['next_url'] ?>">
                        Następna <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>

<!-- Modal zwrotu -->
<div class="modal fade" id="returnModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Zwrot przedmiotu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="returnForm" class="ajax-form" method="POST">
                <?= csrfField() ?>
                
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        Sprawdź stan przedmiotu przed zatwierdzeniem zwrotu.
                    </div>
                    
                    <div class="mb-3">
                        <label for="return_notes" class="form-label">Stan przedmiotu i uwagi</label>
                        <textarea class="form-control" id="return_notes" name="notes" rows="3" 
                                  placeholder="Opisz stan przedmiotu, ewentualne uszkodzenia..."></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-arrow-left-circle me-2"></i>Potwierdź zwrot
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal przedłużenia -->
<div class="modal fade" id="extendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Przedłuż wypożyczenie</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="extendForm" class="ajax-form" method="POST">
                <?= csrfField() ?>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_return_date" class="form-label">Nowa data zwrotu</label>
                        <input type="date" class="form-control" id="new_return_date" name="new_return_date" 
                               min="<?= date('Y-m-d', strtotime('+1 day')) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="extend_notes" class="form-label">Powód przedłużenia</label>
                        <textarea class="form-control" id="extend_notes" name="notes" rows="3" 
                                  placeholder="Podaj powód przedłużenia wypożyczenia..." required></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-calendar-plus me-2"></i>Przedłuż
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showReturnModal(loanId) {
    const form = document.getElementById('returnForm');
    form.action = '/loans/' + loanId + '/return';
    document.getElementById('return_notes').value = '';
    new bootstrap.Modal(document.getElementById('returnModal')).show();
}

function showExtendModal(loanId) {
    const form = document.getElementById('extendForm');
    form.action = '/loans/' + loanId + '/extend';
    document.getElementById('new_return_date').value = '';
    document.getElementById('extend_notes').value = '';
    new bootstrap.Modal(document.getElementById('extendModal')).show();
}
</script>

<?php
$content = ob_get_clean();
include TEMPLATES_PATH . '/layout/main.php';
?>
