<?php
$content = ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="bi bi-calendar-check me-2"></i><?= e($pageTitle) ?>
            </h1>
        </div>
    </div>
</div>

<!-- Filtry -->
<div class="search-filters mb-4">
    <form method="GET" action="/reservations" class="row g-3">
        <div class="col-md-3">
            <label for="status" class="form-label">Status</label>
            <select class="form-select" id="status" name="status">
                <option value="">Wszystkie statusy</option>
                <option value="pending" <?= ($_GET['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Oczekujące</option>
                <option value="approved" <?= ($_GET['status'] ?? '') === 'approved' ? 'selected' : '' ?>>Zatwierdzone</option>
                <option value="rejected" <?= ($_GET['status'] ?? '') === 'rejected' ? 'selected' : '' ?>>Odrzucone</option>
                <option value="cancelled" <?= ($_GET['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>Anulowane</option>
                <option value="fulfilled" <?= ($_GET['status'] ?? '') === 'fulfilled' ? 'selected' : '' ?>>Zrealizowane</option>
            </select>
        </div>
        
        <div class="col-md-3">
            <label for="date_from" class="form-label">Data od</label>
            <input type="date" class="form-control" id="date_from" name="date_from" 
                   value="<?= e($_GET['date_from'] ?? '') ?>">
        </div>
        
        <div class="col-md-3">
            <label for="date_to" class="form-label">Data do</label>
            <input type="date" class="form-control" id="date_to" name="date_to" 
                   value="<?= e($_GET['date_to'] ?? '') ?>">
        </div>
        
        <div class="col-md-3 d-flex align-items-end">
            <button type="submit" class="btn btn-outline-primary me-2">
                <i class="bi bi-search"></i> Filtruj
            </button>
            <a href="/reservations" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i>
            </a>
        </div>
    </form>
</div>

<!-- Lista rezerwacji -->
<?php if (empty($reservations)): ?>
    <div class="text-center py-5">
        <i class="bi bi-calendar-x display-1 text-muted"></i>
        <h4 class="mt-3 text-muted">Brak rezerwacji</h4>
        <p class="text-muted">Nie znaleziono rezerwacji spełniających kryteria.</p>
        <a href="/items" class="btn btn-primary mt-3">
            <i class="bi bi-search me-2"></i>Przeglądaj przedmioty
        </a>
    </div>
<?php else: ?>
    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Przedmiot</th>
                        <?php if (hasRole('admin')): ?>
                            <th>Użytkownik</th>
                        <?php endif; ?>
                        <th>Data potrzeby</th>
                        <th>Data złożenia</th>
                        <th>Status</th>
                        <th>Akcje</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($reservations as $reservation): ?>
                        <tr>
                            <td>
                                <div>
                                    <strong><?= e($reservation['item_name']) ?></strong>
                                    <?php if ($reservation['category_name']): ?>
                                        <br><small class="text-muted"><?= e($reservation['category_name']) ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            
                            <?php if (hasRole('admin')): ?>
                                <td>
                                    <div>
                                        <?= e($reservation['user_name']) ?>
                                        <br><small class="text-muted">@<?= e($reservation['username']) ?></small>
                                    </div>
                                </td>
                            <?php endif; ?>
                            
                            <td>
                                <?= formatDate($reservation['requested_date']) ?>
                                <?php 
                                $daysUntil = (strtotime($reservation['requested_date']) - time()) / (60 * 60 * 24);
                                if ($daysUntil < 0): 
                                ?>
                                    <br><small class="text-danger">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        Termin minął
                                    </small>
                                <?php elseif ($daysUntil <= 3): ?>
                                    <br><small class="text-warning">
                                        <i class="bi bi-clock me-1"></i>
                                        Za <?= ceil($daysUntil) ?> dni
                                    </small>
                                <?php endif; ?>
                            </td>
                            
                            <td>
                                <?= formatDateTime($reservation['created_at']) ?>
                            </td>
                            
                            <td>
                                <span class="badge bg-<?= getStatusColor($reservation['status']) ?>">
                                    <?= getStatusName($reservation['status']) ?>
                                </span>
                            </td>
                            
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <?php if ($reservation['status'] === 'pending' && hasPermission('approve_loans')): ?>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="approveReservation(<?= $reservation['id'] ?>)"
                                                data-bs-toggle="tooltip" title="Zatwierdź">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                        
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="showRejectModal(<?= $reservation['id'] ?>)"
                                                data-bs-toggle="tooltip" title="Odrzuć">
                                            <i class="bi bi-x-circle"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if (in_array($reservation['status'], ['pending', 'approved']) && 
                                              (!hasRole('admin') || $reservation['user_id'] == currentUser()['id'])): ?>
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="cancelReservation(<?= $reservation['id'] ?>)"
                                                data-bs-toggle="tooltip" title="Anuluj"
                                                data-confirm="Czy na pewno chcesz anulować tę rezerwację?">
                                            <i class="bi bi-x-lg"></i>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <?php if ($reservation['status'] === 'approved' && hasPermission('approve_loans')): ?>
                                        <a href="/loans/create?reservation_id=<?= $reservation['id'] ?>" 
                                           class="btn btn-outline-primary"
                                           data-bs-toggle="tooltip" title="Realizuj wypożyczenie">
                                            <i class="bi bi-arrow-right-circle"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        
                        <?php if ($reservation['notes']): ?>
                            <tr class="table-light">
                                <td colspan="<?= hasRole('admin') ? '6' : '5' ?>" class="small">
                                    <i class="bi bi-chat-left-text me-2"></i>
                                    <strong>Uwagi:</strong> <?= e($reservation['notes']) ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
<?php endif; ?>

<!-- Paginacja -->
<?php if (!empty($pagination) && $pagination['total_pages'] > 1): ?>
    <nav aria-label="Paginacja rezerwacji" class="mt-4">
        <ul class="pagination justify-content-center">
            <?php if ($pagination['current_page'] > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['prev_url'] ?>">
                        <i class="bi bi-chevron-left"></i> Poprzednia
                    </a>
                </li>
            <?php endif; ?>
            
            <?php for ($i = $pagination['start_page']; $i <= $pagination['end_page']; $i++): ?>
                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                    <a class="page-link" href="<?= str_replace('page=' . $pagination['current_page'], 'page=' . $i, $_SERVER['REQUEST_URI']) ?>">
                        <?= $i ?>
                    </a>
                </li>
            <?php endfor; ?>
            
            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                <li class="page-item">
                    <a class="page-link" href="<?= $pagination['next_url'] ?>">
                        Następna <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>

<!-- Modal odrzucenia -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Odrzuć rezerwację</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" class="ajax-form" method="POST">
                <?= csrfField() ?>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reject_reason" class="form-label">Powód odrzucenia</label>
                        <textarea class="form-control" id="reject_reason" name="reason" rows="3" 
                                  placeholder="Podaj powód odrzucenia rezerwacji..." required></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anuluj</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-x-circle me-2"></i>Odrzuć rezerwację
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function approveReservation(reservationId) {
    if (confirm('Czy na pewno chcesz zatwierdzić tę rezerwację?')) {
        fetch('/reservations/' + reservationId + '/approve', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '<?= csrfToken() ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                App.showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                App.showAlert('danger', data.error);
            }
        })
        .catch(error => {
            App.showAlert('danger', 'Wystąpił błąd połączenia');
        });
    }
}

function showRejectModal(reservationId) {
    const form = document.getElementById('rejectForm');
    form.action = '/reservations/' + reservationId + '/reject';
    document.getElementById('reject_reason').value = '';
    new bootstrap.Modal(document.getElementById('rejectModal')).show();
}

function cancelReservation(reservationId) {
    if (confirm('Czy na pewno chcesz anulować tę rezerwację?')) {
        fetch('/reservations/' + reservationId + '/cancel', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: '<?= csrfToken() ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                App.showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                App.showAlert('danger', data.error);
            }
        })
        .catch(error => {
            App.showAlert('danger', 'Wystąpił błąd połączenia');
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include TEMPLATES_PATH . '/layout/main.php';
?>
