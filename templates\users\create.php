<?php
/**
 * Formularz dodawania użytkownika
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

// Sprawdzenie uprawnień
if (!$authController->hasPermission('manage_users')) {
    flash('error', 'Brak uprawnień');
    redirect('/dashboard');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-plus text-primary me-2"></i>
                        Dodaj użytkownika
                    </h1>
                    <p class="text-muted mb-0">Utwórz nowe konto użytkownika</p>
                </div>
                <a href="/users" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Powrót do listy
                </a>
            </div>

            <!-- Formularz -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <form action="/users" method="POST" class="needs-validation" novalidate>
                        <?= csrfField() ?>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="border-bottom pb-2 mb-3">Dane podstawowe</h5>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        Nazwa użytkownika <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">@</span>
                                        <input type="text" 
                                               class="form-control" 
                                               id="username" 
                                               name="username" 
                                               required 
                                               pattern="[a-zA-Z0-9_]{3,20}"
                                               placeholder="np. jkowalski">
                                    </div>
                                    <div class="form-text">
                                        3-20 znaków, tylko litery, cyfry i podkreślnik
                                    </div>
                                    <div class="invalid-feedback">
                                        Podaj prawidłową nazwę użytkownika
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        Email <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" 
                                           class="form-control" 
                                           id="email" 
                                           name="email" 
                                           required
                                           placeholder="np. <EMAIL>">
                                    <div class="invalid-feedback">
                                        Podaj prawidłowy adres email
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        Hasło <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           required
                                           minlength="8">
                                    <div class="form-text">
                                        Minimum 8 znaków
                                    </div>
                                    <div class="invalid-feedback">
                                        Hasło musi mieć co najmniej 8 znaków
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password_confirm" class="form-label">
                                        Powtórz hasło <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="password_confirm" 
                                           required
                                           minlength="8">
                                    <div class="invalid-feedback">
                                        Hasła muszą być identyczne
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5 class="border-bottom pb-2 mb-3">Dane osobowe i uprawnienia</h5>
                                
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">
                                        Imię <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="first_name" 
                                           name="first_name" 
                                           required>
                                    <div class="invalid-feedback">
                                        Imię jest wymagane
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">
                                        Nazwisko <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="last_name" 
                                           name="last_name" 
                                           required>
                                    <div class="invalid-feedback">
                                        Nazwisko jest wymagane
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="role" class="form-label">
                                        Rola <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        <option value="geodeta">Geodeta</option>
                                        <option value="admin">Administrator</option>
                                    </select>
                                    <div class="form-text">
                                        Administrator ma pełne uprawnienia w systemie
                                    </div>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           checked>
                                    <label class="form-check-label" for="is_active">
                                        Konto aktywne
                                    </label>
                                    <div class="form-text">
                                        Tylko aktywni użytkownicy mogą logować się do systemu
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <a href="/users" class="btn btn-outline-secondary me-2">Anuluj</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Utwórz użytkownika
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Walidacja formularza
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    const password = document.getElementById('password');
    const passwordConfirm = document.getElementById('password_confirm');
    
    // Sprawdzenie zgodności haseł
    passwordConfirm.addEventListener('input', function() {
        if (password.value !== passwordConfirm.value) {
            passwordConfirm.setCustomValidity('Hasła nie są identyczne');
        } else {
            passwordConfirm.setCustomValidity('');
        }
    });
    
    // Walidacja formularza
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity() || password.value !== passwordConfirm.value) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    }, false);
});
</script>
