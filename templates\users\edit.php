<?php
/**
 * Formularz edycji użytkownika
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

// Sprawdzenie uprawnień
if (!$authController->hasPermission('manage_users')) {
    flash('error', 'Brak uprawnień');
    redirect('/dashboard');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-gear text-primary me-2"></i>
                        Edytuj użytkownika
                    </h1>
                    <p class="text-muted mb-0">
                        <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                    </p>
                </div>
                <div>
                    <a href="/users/<?= $user['id'] ?>" class="btn btn-outline-info me-2">
                        <i class="bi bi-eye me-2"></i>Szczegóły
                    </a>
                    <a href="/users" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Powrót do listy
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Formularz edycji -->
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Dane użytkownika</h5>
                        </div>
                        <div class="card-body">
                            <form action="/users/<?= $user['id'] ?>" method="POST" class="needs-validation" novalidate>
                                <?= csrfField() ?>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            Nazwa użytkownika <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">@</span>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="username" 
                                                   name="username" 
                                                   value="<?= htmlspecialchars($user['username']) ?>"
                                                   required 
                                                   pattern="[a-zA-Z0-9_]{3,20}">
                                        </div>
                                        <div class="form-text">
                                            3-20 znaków, tylko litery, cyfry i podkreślnik
                                        </div>
                                        <div class="invalid-feedback">
                                            Podaj prawidłową nazwę użytkownika
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            Email <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               name="email" 
                                               value="<?= htmlspecialchars($user['email']) ?>"
                                               required>
                                        <div class="invalid-feedback">
                                            Podaj prawidłowy adres email
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">
                                            Imię <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="first_name" 
                                               name="first_name" 
                                               value="<?= htmlspecialchars($user['first_name']) ?>"
                                               required>
                                        <div class="invalid-feedback">
                                            Imię jest wymagane
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">
                                            Nazwisko <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="last_name" 
                                               name="last_name" 
                                               value="<?= htmlspecialchars($user['last_name']) ?>"
                                               required>
                                        <div class="invalid-feedback">
                                            Nazwisko jest wymagane
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">
                                            Rola <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="geodeta" <?= $user['role'] === 'geodeta' ? 'selected' : '' ?>>
                                                Geodeta
                                            </option>
                                            <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>
                                                Administrator
                                            </option>
                                        </select>
                                        <div class="form-text">
                                            Administrator ma pełne uprawnienia w systemie
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Status konta</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   id="is_active" 
                                                   name="is_active" 
                                                   <?= $user['is_active'] ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_active">
                                                Konto aktywne
                                            </label>
                                        </div>
                                        <div class="form-text">
                                            Tylko aktywni użytkownicy mogą logować się do systemu
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-end">
                                    <a href="/users/<?= $user['id'] ?>" class="btn btn-outline-secondary me-2">
                                        Anuluj
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-2"></i>Zapisz zmiany
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Panel resetowania hasła -->
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-key me-2"></i>Resetowanie hasła
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted small mb-3">
                                Wygeneruj nowe hasło dla użytkownika. Zostanie ono wysłane na adres email.
                            </p>
                            
                            <button type="button" 
                                    class="btn btn-warning w-100" 
                                    onclick="resetPassword(<?= $user['id'] ?>)">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                Resetuj hasło
                            </button>
                        </div>
                    </div>
                    
                    <!-- Informacje o koncie -->
                    <div class="card shadow-sm mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>Informacje
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <strong>Utworzono:</strong><br>
                                    <small class="text-muted">
                                        <?= date('d.m.Y H:i', strtotime($user['created_at'])) ?>
                                    </small>
                                </li>
                                <li class="mb-0">
                                    <strong>Ostatnia aktualizacja:</strong><br>
                                    <small class="text-muted">
                                        <?= $user['updated_at'] ? date('d.m.Y H:i', strtotime($user['updated_at'])) : 'Brak' ?>
                                    </small>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Walidacja formularza
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    }, false);
});

// Resetowanie hasła
function resetPassword(userId) {
    if (confirm('Czy na pewno chcesz zresetować hasło tego użytkownika?\n\nNowe hasło zostanie wysłane na adres email.')) {
        fetch(`/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrfToken() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Hasło zostało zresetowane. Nowe hasło zostało wysłane na adres email użytkownika.');
            } else {
                alert('Błąd: ' + (data.error || 'Nie udało się zresetować hasła'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas resetowania hasła');
        });
    }
}
</script>
