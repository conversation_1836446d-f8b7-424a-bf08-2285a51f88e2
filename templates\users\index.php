<?php
/**
 * Lista użytkowników
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

// Sprawdzenie uprawnień
if (!$authController->hasPermission('manage_users')) {
    flash('error', 'Brak uprawnień');
    redirect('/dashboard');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-people text-primary me-2"></i>
                        Użytkownicy
                    </h1>
                    <p class="text-muted mb-0">Zarządzanie kontami użytkowników</p>
                </div>
                <a href="/users/create" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-2"></i>Dodaj użytkownika
                </a>
            </div>

            <!-- Filtry -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Szukaj</label>
                            <input type="text" 
                                   class="form-control" 
                                   id="search" 
                                   name="search" 
                                   placeholder="Imię, nazwisko, email..."
                                   value="<?= htmlspecialchars($_GET['search'] ?? '') ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="role" class="form-label">Rola</label>
                            <select class="form-select" id="role" name="role">
                                <option value="">Wszystkie role</option>
                                <option value="admin" <?= ($_GET['role'] ?? '') === 'admin' ? 'selected' : '' ?>>
                                    Administrator
                                </option>
                                <option value="geodeta" <?= ($_GET['role'] ?? '') === 'geodeta' ? 'selected' : '' ?>>
                                    Geodeta
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Wszystkie</option>
                                <option value="active" <?= ($_GET['status'] ?? '') === 'active' ? 'selected' : '' ?>>
                                    Aktywni
                                </option>
                                <option value="inactive" <?= ($_GET['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>
                                    Nieaktywni
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary w-100">
                                <i class="bi bi-search me-1"></i>Filtruj
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Lista użytkowników -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                Lista użytkowników
                                <span class="badge bg-secondary ms-2"><?= $totalUsers ?? 0 ?></span>
                            </h5>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Użytkownik</th>
                                        <th>Email</th>
                                        <th>Rola</th>
                                        <th>Status</th>
                                        <th>Data utworzenia</th>
                                        <th width="120">Akcje</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-semibold">
                                                            <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                                        </div>
                                                        <small class="text-muted">
                                                            @<?= htmlspecialchars($user['username']) ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="mailto:<?= htmlspecialchars($user['email']) ?>" 
                                                   class="text-decoration-none">
                                                    <?= htmlspecialchars($user['email']) ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php if ($user['role'] === 'admin'): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="bi bi-shield-check me-1"></i>Administrator
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-primary">
                                                        <i class="bi bi-person me-1"></i>Geodeta
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['is_active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>Aktywny
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-x-circle me-1"></i>Nieaktywny
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('d.m.Y', strtotime($user['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/users/<?= $user['id'] ?>" 
                                                       class="btn btn-outline-primary" 
                                                       title="Szczegóły">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="/users/<?= $user['id'] ?>/edit" 
                                                       class="btn btn-outline-secondary" 
                                                       title="Edytuj">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <?php if ($user['id'] !== currentUser()['id']): ?>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger" 
                                                                title="Usuń"
                                                                onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>')">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginacja -->
                        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                            <div class="card-footer">
                                <nav aria-label="Paginacja użytkowników">
                                    <ul class="pagination pagination-sm justify-content-center mb-0">
                                        <?php if ($pagination['current_page'] > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?= $pagination['prev_url'] ?>">
                                                    <i class="bi bi-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php for ($i = $pagination['start_page']; $i <= $pagination['end_page']; $i++): ?>
                                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                                <a class="page-link" href="<?= str_replace('{page}', $i, $pagination['url_pattern']) ?>">
                                                    <?= $i ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?= $pagination['next_url'] ?>">
                                                    <i class="bi bi-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people display-1 text-muted"></i>
                            <h5 class="mt-3">Brak użytkowników</h5>
                            <p class="text-muted">Nie znaleziono użytkowników spełniających kryteria wyszukiwania.</p>
                            <a href="/users/create" class="btn btn-primary">
                                <i class="bi bi-plus-lg me-2"></i>Dodaj pierwszego użytkownika
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteUser(userId, userName) {
    if (confirm(`Czy na pewno chcesz usunąć użytkownika "${userName}"?\n\nTa operacja jest nieodwracalna.`)) {
        fetch(`/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Błąd: ' + (data.error || 'Nie udało się usunąć użytkownika'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas usuwania użytkownika');
        });
    }
}
</script>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: 600;
}
</style>
