<?php
/**
 * Profil użytkownika
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

$currentUser = currentUser();

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person-circle text-primary me-2"></i>
                        Mój profil
                    </h1>
                    <p class="text-muted mb-0">Zarządzaj swoimi danymi osobowymi</p>
                </div>
                <a href="/dashboard" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>Powrót do panelu
                </a>
            </div>

            <div class="row">
                <!-- Informacje o użytkowniku -->
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person me-2"></i>
                                Dane osobowe
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="/profile/update" method="POST" class="needs-validation" novalidate>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">
                                            Imię <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="first_name" 
                                               name="first_name" 
                                               value="<?= htmlspecialchars($currentUser['first_name']) ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            Imię jest wymagane
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">
                                            Nazwisko <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="last_name" 
                                               name="last_name" 
                                               value="<?= htmlspecialchars($currentUser['last_name']) ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            Nazwisko jest wymagane
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">
                                            Nazwa użytkownika <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="username" 
                                               name="username" 
                                               value="<?= htmlspecialchars($currentUser['username']) ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            Nazwa użytkownika jest wymagana
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">
                                            Email <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" 
                                               class="form-control" 
                                               id="email" 
                                               name="email" 
                                               value="<?= htmlspecialchars($currentUser['email']) ?>" 
                                               required>
                                        <div class="invalid-feedback">
                                            Podaj prawidłowy adres email
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">Rola</label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="role" 
                                               value="<?= $currentUser['role'] == 'admin' ? 'Administrator' : 'Geodeta' ?>" 
                                               readonly>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg me-2"></i>
                                        Zapisz zmiany
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Zmiana hasła -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-shield-lock me-2"></i>
                                Zmiana hasła
                            </h5>
                        </div>
                        <div class="card-body">
                            <form action="/profile/change-password" method="POST" class="needs-validation" novalidate>
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">
                                        Obecne hasło <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" 
                                           class="form-control" 
                                           id="current_password" 
                                           name="current_password" 
                                           required>
                                    <div class="invalid-feedback">
                                        Podaj obecne hasło
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="new_password" class="form-label">
                                            Nowe hasło <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" 
                                               class="form-control" 
                                               id="new_password" 
                                               name="new_password" 
                                               minlength="8" 
                                               required>
                                        <div class="invalid-feedback">
                                            Hasło musi mieć co najmniej 8 znaków
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="confirm_password" class="form-label">
                                            Potwierdź hasło <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" 
                                               class="form-control" 
                                               id="confirm_password" 
                                               name="confirm_password" 
                                               required>
                                        <div class="invalid-feedback">
                                            Hasła muszą być identyczne
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-key me-2"></i>
                                    Zmień hasło
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Sidebar z informacjami -->
                <div class="col-lg-4">
                    <!-- Statystyki użytkownika -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-graph-up me-2"></i>
                                Twoje statystyki
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0">0</h4>
                                        <small class="text-muted">Aktywne wypożyczenia</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-0">0</h4>
                                    <small class="text-muted">Łącznie wypożyczeń</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informacje o koncie -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                Informacje o koncie
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <strong>Data utworzenia:</strong><br>
                                    <small class="text-muted">
                                        <?= date('d.m.Y H:i', strtotime($currentUser['created_at'])) ?>
                                    </small>
                                </li>
                                <li class="mb-2">
                                    <strong>Ostatnia aktualizacja:</strong><br>
                                    <small class="text-muted">
                                        <?= $currentUser['updated_at'] ? date('d.m.Y H:i', strtotime($currentUser['updated_at'])) : 'Brak' ?>
                                    </small>
                                </li>
                                <li class="mb-0">
                                    <strong>Status konta:</strong><br>
                                    <span class="badge bg-success">Aktywne</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Walidacja formularzy
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // Walidacja potwierdzenia hasła
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    if (newPassword && confirmPassword) {
        confirmPassword.addEventListener('input', function() {
            if (newPassword.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Hasła muszą być identyczne');
            } else {
                confirmPassword.setCustomValidity('');
            }
        });
    }
})();
</script>
