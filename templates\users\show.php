<?php
/**
 * Szczegóły użytkownika
 */

// Sprawdzenie czy użytkownik jest zalogowany
$authController = new App\Controllers\AuthController();
if (!$authController->isLoggedIn()) {
    redirect('/login');
}

// Sprawdzenie uprawnień
if (!$authController->hasPermission('manage_users')) {
    flash('error', 'Brak uprawnień');
    redirect('/dashboard');
}

include TEMPLATES_PATH . '/layout/main.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Nagłówek -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-person text-primary me-2"></i>
                        <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                    </h1>
                    <p class="text-muted mb-0">
                        @<?= htmlspecialchars($user['username']) ?> • 
                        <?= htmlspecialchars($user['email']) ?>
                    </p>
                </div>
                <div>
                    <a href="/users/<?= $user['id'] ?>/edit" class="btn btn-primary me-2">
                        <i class="bi bi-pencil me-2"></i>Edytuj
                    </a>
                    <a href="/users" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Powrót do listy
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informacje podstawowe -->
                <div class="col-lg-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person-badge me-2"></i>Informacje podstawowe
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Imię i nazwisko</label>
                                        <p class="mb-0">
                                            <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                        </p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Nazwa użytkownika</label>
                                        <p class="mb-0">
                                            <code>@<?= htmlspecialchars($user['username']) ?></code>
                                        </p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Adres email</label>
                                        <p class="mb-0">
                                            <a href="mailto:<?= htmlspecialchars($user['email']) ?>" 
                                               class="text-decoration-none">
                                                <?= htmlspecialchars($user['email']) ?>
                                            </a>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Rola</label>
                                        <p class="mb-0">
                                            <?php if ($user['role'] === 'admin'): ?>
                                                <span class="badge bg-danger fs-6">
                                                    <i class="bi bi-shield-check me-1"></i>Administrator
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-primary fs-6">
                                                    <i class="bi bi-person me-1"></i>Geodeta
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Status konta</label>
                                        <p class="mb-0">
                                            <?php if ($user['is_active']): ?>
                                                <span class="badge bg-success fs-6">
                                                    <i class="bi bi-check-circle me-1"></i>Aktywne
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary fs-6">
                                                    <i class="bi bi-x-circle me-1"></i>Nieaktywne
                                                </span>
                                            <?php endif; ?>
                                        </p>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold">Data utworzenia</label>
                                        <p class="mb-0">
                                            <?= date('d.m.Y H:i', strtotime($user['created_at'])) ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Uprawnienia -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-shield-lock me-2"></i>Uprawnienia
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php 
                            $permissions = CONFIG['roles'][$user['role']]['permissions'] ?? [];
                            $permissionLabels = [
                                'manage_items' => 'Zarządzanie przedmiotami',
                                'manage_users' => 'Zarządzanie użytkownikami',
                                'approve_loans' => 'Zatwierdzanie wypożyczeń',
                                'approve_transfers' => 'Zatwierdzanie transferów',
                                'view_all_reports' => 'Przeglądanie wszystkich raportów',
                                'system_settings' => 'Ustawienia systemu',
                                'view_items' => 'Przeglądanie przedmiotów',
                                'make_reservations' => 'Tworzenie rezerwacji',
                                'request_transfers' => 'Wnioskowanie o transfery',
                                'view_own_loans' => 'Przeglądanie własnych wypożyczeń',
                                'view_own_reports' => 'Przeglądanie własnych raportów'
                            ];
                            ?>
                            
                            <div class="row">
                                <?php foreach ($permissions as $permission): ?>
                                    <div class="col-md-6 mb-2">
                                        <span class="badge bg-light text-dark border">
                                            <i class="bi bi-check text-success me-1"></i>
                                            <?= $permissionLabels[$permission] ?? $permission ?>
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (empty($permissions)): ?>
                                <p class="text-muted mb-0">Brak przypisanych uprawnień</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Panel akcji -->
                <div class="col-lg-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-gear me-2"></i>Akcje
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/users/<?= $user['id'] ?>/edit" class="btn btn-primary">
                                    <i class="bi bi-pencil me-2"></i>Edytuj użytkownika
                                </a>
                                
                                <button type="button" 
                                        class="btn btn-warning" 
                                        onclick="resetPassword(<?= $user['id'] ?>)">
                                    <i class="bi bi-key me-2"></i>Resetuj hasło
                                </button>
                                
                                <?php if ($user['id'] !== currentUser()['id']): ?>
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteUser(<?= $user['id'] ?>, '<?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>')">
                                        <i class="bi bi-trash me-2"></i>Usuń użytkownika
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Statystyki -->
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-graph-up me-2"></i>Statystyki
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-0">0</h4>
                                        <small class="text-muted">Aktywne wypożyczenia</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-0">0</h4>
                                    <small class="text-muted">Łącznie wypożyczeń</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Resetowanie hasła
function resetPassword(userId) {
    if (confirm('Czy na pewno chcesz zresetować hasło tego użytkownika?\n\nNowe hasło zostanie wysłane na adres email.')) {
        fetch(`/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrfToken() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Hasło zostało zresetowane. Nowe hasło zostało wysłane na adres email użytkownika.');
            } else {
                alert('Błąd: ' + (data.error || 'Nie udało się zresetować hasła'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas resetowania hasła');
        });
    }
}

// Usuwanie użytkownika
function deleteUser(userId, userName) {
    if (confirm(`Czy na pewno chcesz usunąć użytkownika "${userName}"?\n\nTa operacja jest nieodwracalna.`)) {
        fetch(`/users/${userId}/delete`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'X-CSRF-Token': '<?= csrfToken() ?>'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/users';
            } else {
                alert('Błąd: ' + (data.error || 'Nie udało się usunąć użytkownika'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Wystąpił błąd podczas usuwania użytkownika');
        });
    }
}
</script>
