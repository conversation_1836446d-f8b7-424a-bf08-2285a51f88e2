<?php
/**
 * Test połączenia z bazą danych
 * Wywołaj: https://twoja-domena.com/magazyn/test_database.php
 */

echo "<h1>🔍 Test połączenia z bazą danych</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Ładowanie konfiguracji
try {
    // Ładowanie zmiennych środowiskowych
    if (file_exists(__DIR__ . '/.env')) {
        $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '=') !== false && substr($line, 0, 1) !== '#') {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value);
            }
        }
    }
    
    echo "<p class='success'>✅ Plik .env załadowany</p>";
    
    // Dane połączenia
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $port = $_ENV['DB_PORT'] ?? 3306;
    $dbname = $_ENV['DB_NAME'] ?? '';
    $username = $_ENV['DB_USERNAME'] ?? '';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    echo "<h2>📋 Dane połączenia:</h2>";
    echo "<ul>";
    echo "<li><strong>Host:</strong> {$host}</li>";
    echo "<li><strong>Port:</strong> {$port}</li>";
    echo "<li><strong>Baza:</strong> {$dbname}</li>";
    echo "<li><strong>Użytkownik:</strong> {$username}</li>";
    echo "<li><strong>Hasło:</strong> " . (empty($password) ? 'BRAK' : str_repeat('*', strlen($password))) . "</li>";
    echo "</ul>";
    
    // Test połączenia
    echo "<h2>🔌 Test połączenia z bazą danych:</h2>";
    
    $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p class='success'>✅ Połączenie z bazą danych udane!</p>";
    
    // Test tabel
    echo "<h2>📊 Sprawdzenie tabel:</h2>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p class='error'>❌ Brak tabel w bazie danych!</p>";
        echo "<p class='info'>💡 Musisz zaimportować plik database_schema.sql</p>";
    } else {
        echo "<p class='success'>✅ Znaleziono " . count($tables) . " tabel:</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
    }
    
    // Test użytkowników
    if (in_array('users', $tables)) {
        echo "<h2>👥 Test tabeli users:</h2>";
        
        $userCount = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
        echo "<p class='info'>📊 Liczba użytkowników: {$userCount}</p>";
        
        if ($userCount > 0) {
            echo "<h3>Lista użytkowników:</h3>";
            $users = $pdo->query("SELECT username, email, role, is_active FROM users")->fetchAll();
            echo "<table border='1' style='border-collapse:collapse;'>";
            echo "<tr><th>Username</th><th>Email</th><th>Role</th><th>Active</th></tr>";
            foreach ($users as $user) {
                $activeStatus = $user['is_active'] ? '✅' : '❌';
                echo "<tr>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>{$activeStatus}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Sprawdzenie użytkownika admin
            $admin = $pdo->prepare("SELECT * FROM users WHERE username = ?");
            $admin->execute(['admin']);
            $adminUser = $admin->fetch();
            
            if ($adminUser) {
                echo "<h3>🔍 Szczegóły konta admin:</h3>";
                echo "<ul>";
                echo "<li><strong>Username:</strong> {$adminUser['username']}</li>";
                echo "<li><strong>Email:</strong> {$adminUser['email']}</li>";
                echo "<li><strong>Role:</strong> {$adminUser['role']}</li>";
                echo "<li><strong>Active:</strong> " . ($adminUser['is_active'] ? 'TAK' : 'NIE') . "</li>";
                echo "<li><strong>Password hash:</strong> " . substr($adminUser['password_hash'], 0, 20) . "...</li>";
                echo "</ul>";
                
                // Test hasła
                $testPassword = 'admin123';
                if (password_verify($testPassword, $adminUser['password_hash'])) {
                    echo "<p class='success'>✅ Hasło 'admin123' jest POPRAWNE!</p>";
                } else {
                    echo "<p class='error'>❌ Hasło 'admin123' jest BŁĘDNE!</p>";
                }
            } else {
                echo "<p class='error'>❌ Użytkownik 'admin' nie istnieje!</p>";
            }
        }
    }
    
    // Test prób logowania
    if (in_array('login_attempts', $tables)) {
        echo "<h2>🚫 Sprawdzenie blokad logowania:</h2>";
        
        $attempts = $pdo->query("SELECT username, attempts, last_attempt FROM login_attempts WHERE username = 'admin'")->fetchAll();
        
        if (empty($attempts)) {
            echo "<p class='success'>✅ Brak blokad dla użytkownika admin</p>";
        } else {
            echo "<p class='error'>❌ Znaleziono blokady:</p>";
            foreach ($attempts as $attempt) {
                echo "<p>Username: {$attempt['username']}, Próby: {$attempt['attempts']}, Ostatnia: {$attempt['last_attempt']}</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ BŁĄD: " . $e->getMessage() . "</p>";
    echo "<p class='info'>💡 Sprawdź dane połączenia w pliku .env</p>";
}

echo "<hr>";
echo "<p><strong>🔧 Aby naprawić problemy:</strong></p>";
echo "<ol>";
echo "<li>Jeśli brak tabel - zaimportuj database_schema.sql</li>";
echo "<li>Jeśli brak użytkownika admin - zaimportuj unlock_admin.sql</li>";
echo "<li>Jeśli są blokady - zaimportuj unlock_admin.sql</li>";
echo "</ol>";

echo "<p><a href='/magazyn/'>← Powrót do logowania</a></p>";
?>
