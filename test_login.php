<?php
/**
 * Skrypt testowy do sprawdzenia logowania
 */

// Bootstrap aplikacji
require_once __DIR__ . '/config/bootstrap.php';

echo "<h2>🧪 Test logowania użytkowników</h2>";

try {
    // Sprawdź użytkowników w bazie
    $users = DB->select("SELECT id, username, role, is_active FROM users ORDER BY id");
    
    echo "<h3>👥 Użytkownicy w systemie:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Rola</th><th>Aktywny</th></tr>";
    
    foreach ($users as $user) {
        $activeText = $user['is_active'] ? '✅ Tak' : '❌ Nie';
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['role']}</td>";
        echo "<td>{$activeText}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Sprawdź sesje
    $sessions = DB->select("SELECT COUNT(*) as count FROM user_sessions");
    echo "<h3>📊 Sesje w bazie: " . $sessions[0]['count'] . "</h3>";
    
    // Sprawdź aktualną sesję PHP
    echo "<h3>🔍 Aktualna sesja PHP:</h3>";
    if (isset($_SESSION['user'])) {
        echo "<p>✅ Zalogowany jako: <strong>" . $_SESSION['user']['username'] . "</strong> (rola: " . $_SESSION['user']['role'] . ")</p>";
        echo "<p>🕒 Czas logowania: " . (isset($_SESSION['login_time']) ? date('Y-m-d H:i:s', $_SESSION['login_time']) : 'Nieznany') . "</p>";
    } else {
        echo "<p>❌ Nie zalogowany</p>";
    }
    
    // Test funkcji isLoggedIn()
    echo "<h3>🔧 Test funkcji isLoggedIn():</h3>";
    $isLoggedIn = isLoggedIn();
    echo "<p>Wynik: " . ($isLoggedIn ? '✅ TRUE' : '❌ FALSE') . "</p>";
    
    // Linki testowe
    echo "<h3>🔗 Linki testowe:</h3>";
    echo "<p><a href='/login'>🔑 Strona logowania</a></p>";
    echo "<p><a href='/dashboard'>📊 Dashboard admin</a></p>";
    echo "<p><a href='/dashboard/geodeta'>👷 Dashboard geodeta</a></p>";
    echo "<p><a href='/logout'>🚪 Wyloguj</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Błąd: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><small>💡 Jeśli widzisz błędy, sprawdź logi serwera i upewnij się, że wszystkie pliki zostały skopiowane.</small></p>";
?>
