<?php
/**
 * Test hasła - sprawdza czy hash jest poprawny
 * Wy<PERSON>łaj: https://twoja-domena.com/magazyn/test_password.php
 */

echo "<h1>🔐 Test hasła administratora</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Test różnych hashów hasła "admin123"
$password = 'admin123';

echo "<h2>🧪 Testowanie hashów hasła: <strong>{$password}</strong></h2>";

// Hash 1 - stary (błędny)
$oldHash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm';
echo "<h3>Hash 1 (stary):</h3>";
echo "<p><code>{$oldHash}</code></p>";
if (password_verify($password, $oldHash)) {
    echo "<p class='success'>✅ PASUJE!</p>";
} else {
    echo "<p class='error'>❌ NIE PASUJE</p>";
}

// Hash 2 - nowy (poprawny)
$newHash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
echo "<h3>Hash 2 (nowy):</h3>";
echo "<p><code>{$newHash}</code></p>";
if (password_verify($password, $newHash)) {
    echo "<p class='success'>✅ PASUJE!</p>";
} else {
    echo "<p class='error'>❌ NIE PASUJE</p>";
}

// Wygeneruj nowy hash na żywo
echo "<h3>Hash 3 (wygenerowany na żywo):</h3>";
$liveHash = password_hash($password, PASSWORD_DEFAULT);
echo "<p><code>{$liveHash}</code></p>";
if (password_verify($password, $liveHash)) {
    echo "<p class='success'>✅ PASUJE!</p>";
} else {
    echo "<p class='error'>❌ NIE PASUJE</p>";
}

// Test połączenia z bazą i sprawdzenie hasła w bazie
try {
    // Ładowanie zmiennych środowiskowych
    if (file_exists(__DIR__ . '/.env')) {
        $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '=') !== false && substr($line, 0, 1) !== '#') {
                list($key, $value) = explode('=', $line, 2);
                $_ENV[trim($key)] = trim($value);
            }
        }
    }
    
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $port = $_ENV['DB_PORT'] ?? 3306;
    $dbname = $_ENV['DB_NAME'] ?? '';
    $username = $_ENV['DB_USERNAME'] ?? '';
    $dbPassword = $_ENV['DB_PASSWORD'] ?? '';
    
    $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $dbPassword, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>🗄️ Test hasła w bazie danych:</h2>";
    
    $stmt = $pdo->prepare("SELECT password_hash FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $user = $stmt->fetch();
    
    if ($user) {
        $dbHash = $user['password_hash'];
        echo "<h3>Hash z bazy danych:</h3>";
        echo "<p><code>{$dbHash}</code></p>";
        
        if (password_verify($password, $dbHash)) {
            echo "<p class='success'>✅ HASŁO W BAZIE JEST POPRAWNE!</p>";
        } else {
            echo "<p class='error'>❌ HASŁO W BAZIE JEST BŁĘDNE!</p>";
            echo "<p class='info'>💡 Wykonaj fix_login.sql aby naprawić</p>";
        }
    } else {
        echo "<p class='error'>❌ Użytkownik admin nie istnieje w bazie!</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Błąd połączenia z bazą: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>🔧 Instrukcje naprawy:</h2>";
echo "<ol>";
echo "<li>Wykonaj plik <strong>fix_login.sql</strong> w phpMyAdmin</li>";
echo "<li>Odśwież tę stronę aby sprawdzić czy naprawione</li>";
echo "<li>Spróbuj się zalogować: admin / admin123</li>";
echo "</ol>";

echo "<p><a href='/magazyn/'>← Powrót do logowania</a></p>";
?>
