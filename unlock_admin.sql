-- Odblokowanie konta administratora i reset prób logowania

-- Usuń wszystkie nieudane próby logowania dla admin
DELETE FROM login_attempts WHERE username = 'admin';

-- <PERSON><PERSON><PERSON><PERSON> się, że konto admin jest aktywne i ma poprawne dane
UPDATE users SET 
    password_hash = '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm',
    is_active = 1,
    role = 'admin',
    updated_at = NOW()
WHERE username = 'admin';

-- Sprawd<PERSON>ie stanu konta
SELECT username, email, role, is_active, created_at, updated_at FROM users WHERE username = 'admin';

-- S<PERSON><PERSON><PERSON><PERSON><PERSON> czy są jakieś próby logowania
SELECT * FROM login_attempts WHERE username = 'admin';

-- DAN<PERSON> DO LOGOWANIA PO ODBLOKOWANIU:
-- Użytkownik: admin
-- Hasło: admin123
