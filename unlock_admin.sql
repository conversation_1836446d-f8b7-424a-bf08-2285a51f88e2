-- Odblokowanie konta administratora i reset prób logowania

-- Na<PERSON><PERSON><PERSON>w utwórz tabelę login_attempts jeśli nie istnieje
CREATE TABLE IF NOT EXISTS login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    attempts INT DEFAULT 1,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address)
);

-- <PERSON><PERSON><PERSON> wszystkie nieudane próby logowania dla admin
DELETE FROM login_attempts WHERE username = 'admin';

-- <PERSON><PERSON><PERSON><PERSON> si<PERSON>, że konto admin jest aktywne i ma poprawne dane
-- Hash dla hasła: admin123
UPDATE users SET
    password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    is_active = 1,
    role = 'admin',
    updated_at = NOW()
WHERE username = 'admin';

-- Sprawdzenie stanu konta
SELECT username, email, role, is_active, created_at, updated_at FROM users WHERE username = 'admin';

-- Sprawdzenie czy są jakieś próby logowania
SELECT * FROM login_attempts WHERE username = 'admin';

-- DANE DO LOGOWANIA PO ODBLOKOWANIU:
-- Użytkownik: admin
-- Hasło: admin123
