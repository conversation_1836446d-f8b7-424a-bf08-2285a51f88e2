<?php

/**
 * Autoloader dla hostingu bez Composer
 * Ten plik zastępuje Composer autoloader dla podstawowych hostingów
 */

// Mapa klas do plików
$classMap = [
    // Database
    'App\\Database\\Database' => __DIR__ . '/../src/Database/Database.php',
    
    // Models
    'App\\Models\\User' => __DIR__ . '/../src/Models/User.php',
    'App\\Models\\Item' => __DIR__ . '/../src/Models/Item.php',
    'App\\Models\\Category' => __DIR__ . '/../src/Models/Category.php',
    'App\\Models\\ItemImage' => __DIR__ . '/../src/Models/ItemImage.php',
    'App\\Models\\Reservation' => __DIR__ . '/../src/Models/Reservation.php',
    'App\\Models\\Loan' => __DIR__ . '/../src/Models/Loan.php',
    'App\\Models\\TransferRequest' => __DIR__ . '/../src/Models/TransferRequest.php',
    
    // Controllers
    'App\\Controllers\\AuthController' => __DIR__ . '/../src/Controllers/AuthController.php',
    'App\\Controllers\\DashboardController' => __DIR__ . '/../src/Controllers/DashboardController.php',
    'App\\Controllers\\ItemController' => __DIR__ . '/../src/Controllers/ItemController.php',
    'App\\Controllers\\CategoryController' => __DIR__ . '/../src/Controllers/CategoryController.php',
    'App\\Controllers\\ReservationController' => __DIR__ . '/../src/Controllers/ReservationController.php',
    'App\\Controllers\\LoanController' => __DIR__ . '/../src/Controllers/LoanController.php',
    'App\\Controllers\\TransferController' => __DIR__ . '/../src/Controllers/TransferController.php',
    'App\\Controllers\\UserController' => __DIR__ . '/../src/Controllers/UserController.php',
    'App\\Controllers\\ReportController' => __DIR__ . '/../src/Controllers/ReportController.php',
    
    // API Controllers
    'App\\Controllers\\Api\\ItemController' => __DIR__ . '/../src/Controllers/Api/ItemController.php',
    'App\\Controllers\\Api\\UserController' => __DIR__ . '/../src/Controllers/Api/UserController.php',
    
    // Services
    'App\\Services\\ImageUploadService' => __DIR__ . '/../src/Services/ImageUploadService.php',
    'App\\Services\\ReportService' => __DIR__ . '/../src/Services/ReportService.php',
    
    // Utils
    'App\\Utils\\Router' => __DIR__ . '/../src/Utils/Router.php',
    'App\\Utils\\Validator' => __DIR__ . '/../src/Utils/Validator.php',
];

// Rejestracja autoloadera
spl_autoload_register(function ($className) use ($classMap) {
    if (isset($classMap[$className])) {
        $file = $classMap[$className];
        if (file_exists($file)) {
            require_once $file;
            return true;
        }
    }
    
    // Fallback - próba automatycznego mapowania
    $file = __DIR__ . '/../src/' . str_replace(['App\\', '\\'], ['', '/'], $className) . '.php';
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    return false;
});

// Debug autoloadera
if (isset($_GET['debug_autoloader'])) {
    echo "<h3>Debug autoloadera:</h3>";
    foreach ($classMap as $class => $file) {
        echo "<p>{$class} -> {$file} -> " . (file_exists($file) ? "OK" : "BRAK") . "</p>";
    }
    exit;
}
